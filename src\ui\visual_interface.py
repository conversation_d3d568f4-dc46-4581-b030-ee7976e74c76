#!/usr/bin/env python3
"""
Interfaz visual para Infología con plantilla en pantalla
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
from pathlib import Path
import json

# Importar módulos del sistema
from src.text_processing.clinical_text_processor import ClinicalTextProcessor
from src.image_processing.image_processor import ClinicalImageProcessor
from src.report_generation.report_generator import ClinicalReportGenerator


class VisualInterface:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🏥 Infología - Generador Visual de Informes")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # Inicializar procesadores
        self.text_processor = ClinicalTextProcessor()
        self.image_processor = ClinicalImageProcessor()
        self.report_generator = ClinicalReportGenerator()
        
        # Variables
        self.template_images = []  # Imágenes de las diapositivas
        self.current_slide = 0
        self.slide_image_mapping = {}  # Mapeo de imágenes por diapositiva
        self.patient_data = {}
        self.clinical_content = ""
        
        # Variables de configuración
        self.remove_background = tk.BooleanVar(value=True)
        self.auto_adjust = tk.BooleanVar(value=True)
        
        self.setup_ui()
        self.load_template_preview()
        
    def setup_ui(self):
        """Configura la interfaz visual"""
        # Estilo
        style = ttk.Style()
        style.theme_use('clam')
        
        # Frame principal
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Título con dedicatoria
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(title_frame, text="🏥 Infología - Editor Visual", 
                               font=('Arial', 18, 'bold'))
        title_label.pack()
        
        dedication_label = ttk.Label(title_frame, text="Para: Mi amor", 
                                   font=('Arial', 10, 'italic'), foreground='gray')
        dedication_label.pack()
        
        # Frame principal dividido
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # === PANEL IZQUIERDO: CONTROLES ===
        left_panel = ttk.Frame(content_frame, width=350)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_panel.pack_propagate(False)
        
        self.setup_left_panel(left_panel)
        
        # === PANEL DERECHO: VISTA DE PLANTILLA ===
        right_panel = ttk.Frame(content_frame)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.setup_right_panel(right_panel)
        
    def setup_left_panel(self, parent):
        """Configura el panel izquierdo con controles"""
        
        # === DATOS DEL PACIENTE ===
        patient_frame = ttk.LabelFrame(parent, text="👤 Datos del Paciente", padding=10)
        patient_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Variables del paciente
        self.patient_name = tk.StringVar()
        self.patient_age = tk.StringVar()
        self.patient_sex = tk.StringVar(value="Femenino")
        self.patient_civil = tk.StringVar(value="Soltero")
        self.patient_occupation = tk.StringVar()
        self.doctor_name = tk.StringVar()
        
        # Campos del paciente
        fields = [
            ("Nombre:", self.patient_name),
            ("Edad:", self.patient_age),
            ("Ocupación:", self.patient_occupation),
            ("Doctor:", self.doctor_name)
        ]
        
        for i, (label, var) in enumerate(fields):
            row = ttk.Frame(patient_frame)
            row.pack(fill=tk.X, pady=2)
            ttk.Label(row, text=label, width=12).pack(side=tk.LEFT)
            ttk.Entry(row, textvariable=var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Combos
        sex_frame = ttk.Frame(patient_frame)
        sex_frame.pack(fill=tk.X, pady=2)
        ttk.Label(sex_frame, text="Sexo:", width=12).pack(side=tk.LEFT)
        ttk.Combobox(sex_frame, textvariable=self.patient_sex, 
                    values=["Femenino", "Masculino"], state="readonly").pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        civil_frame = ttk.Frame(patient_frame)
        civil_frame.pack(fill=tk.X, pady=2)
        ttk.Label(civil_frame, text="Estado civil:", width=12).pack(side=tk.LEFT)
        ttk.Combobox(civil_frame, textvariable=self.patient_civil,
                    values=["Soltero", "Casado", "Divorciado", "Viudo"], state="readonly").pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # === CONTENIDO CLÍNICO ===
        clinical_frame = ttk.LabelFrame(parent, text="📝 Contenido Clínico", padding=10)
        clinical_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Área de texto
        text_frame = ttk.Frame(clinical_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.clinical_text = tk.Text(text_frame, height=15, wrap=tk.WORD, font=('Consolas', 9))
        text_scroll = ttk.Scrollbar(text_frame, orient="vertical", command=self.clinical_text.yview)
        self.clinical_text.configure(yscrollcommand=text_scroll.set)
        
        self.clinical_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Placeholder
        placeholder = """MOTIVO DE CONSULTA:
Escriba aquí el motivo...

ENFERMEDAD ACTUAL:
Describa la enfermedad actual...

ANTECEDENTES MÉDICOS:
Liste los antecedentes...

DIAGNÓSTICO:
Escriba el diagnóstico...

PLAN DE TRATAMIENTO:
Describa el plan..."""
        
        self.clinical_text.insert(tk.END, placeholder)
        self.clinical_text.bind('<FocusIn>', self.clear_placeholder)
        
        # === CONFIGURACIÓN ===
        config_frame = ttk.LabelFrame(parent, text="⚙️ Configuración", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(config_frame, text="🎨 Remover fondo de imágenes", 
                       variable=self.remove_background).pack(anchor=tk.W)
        
        ttk.Checkbutton(config_frame, text="📐 Ajuste automático", 
                       variable=self.auto_adjust).pack(anchor=tk.W)
        
        # === BOTONES ===
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="🚀 GENERAR INFORME", 
                  command=self.generate_report, style='Accent.TButton').pack(fill=tk.X, pady=2)
        
        ttk.Button(buttons_frame, text="🔄 Actualizar Vista", 
                  command=self.update_preview).pack(fill=tk.X, pady=2)
        
        ttk.Button(buttons_frame, text="🗑️ Limpiar Todo", 
                  command=self.clear_all).pack(fill=tk.X, pady=2)
        
    def setup_right_panel(self, parent):
        """Configura el panel derecho con vista de plantilla"""
        
        # Título del panel
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="📋 Vista de Plantilla", 
                 font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        
        # Controles de navegación
        nav_frame = ttk.Frame(title_frame)
        nav_frame.pack(side=tk.RIGHT)
        
        ttk.Button(nav_frame, text="◀", command=self.prev_slide).pack(side=tk.LEFT, padx=2)
        
        self.slide_label = ttk.Label(nav_frame, text="Diapositiva 1/42")
        self.slide_label.pack(side=tk.LEFT, padx=10)
        
        ttk.Button(nav_frame, text="▶", command=self.next_slide).pack(side=tk.LEFT, padx=2)
        
        # Canvas para mostrar la diapositiva
        canvas_frame = ttk.Frame(parent, relief=tk.SUNKEN, borderwidth=2)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(canvas_frame, bg='white', cursor='crosshair')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Configurar drag & drop
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)
        
        # Menú contextual
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="📁 Agregar Imagen", command=self.add_image_to_slide)
        self.context_menu.add_command(label="🗑️ Eliminar Imagen", command=self.remove_image_from_slide)
        
        self.canvas.bind("<Button-3>", self.show_context_menu)
        
        # Instrucciones
        instructions_frame = ttk.Frame(parent)
        instructions_frame.pack(fill=tk.X, pady=(10, 0))
        
        instructions = ttk.Label(instructions_frame, 
                               text="💡 Haz clic derecho para agregar imágenes • Arrastra para ajustar posición",
                               font=('Arial', 9), foreground='gray')
        instructions.pack()
        
    def clear_placeholder(self, event):
        """Limpia el placeholder del texto clínico"""
        current_text = self.clinical_text.get("1.0", tk.END)
        if "MOTIVO DE CONSULTA:" in current_text and "Escriba aquí el motivo" in current_text:
            self.clinical_text.delete("1.0", tk.END)
    
    def load_template_preview(self):
        """Carga la vista previa de la plantilla"""
        # Por ahora mostrar un placeholder
        self.canvas.create_text(400, 300, text="🏥 PLANTILLA INFOLOGÍA\n\nVista previa de la diapositiva\n\nHaz clic derecho para agregar imágenes", 
                               font=('Arial', 16), fill='gray', justify=tk.CENTER)
    
    def prev_slide(self):
        """Navega a la diapositiva anterior"""
        if self.current_slide > 0:
            self.current_slide -= 1
            self.update_slide_view()
    
    def next_slide(self):
        """Navega a la siguiente diapositiva"""
        if self.current_slide < 41:  # 42 diapositivas (0-41)
            self.current_slide += 1
            self.update_slide_view()
    
    def update_slide_view(self):
        """Actualiza la vista de la diapositiva actual"""
        self.slide_label.config(text=f"Diapositiva {self.current_slide + 1}/42")
        self.canvas.delete("all")
        
        # Mostrar contenido de la diapositiva
        slide_title = f"Diapositiva {self.current_slide + 1}"
        if self.current_slide == 0:
            slide_title = "Portada"
        elif self.current_slide == 1:
            slide_title = "Datos Personales"
        elif 17 <= self.current_slide <= 27:
            slide_title = "Evidencia Clínica"
        
        self.canvas.create_text(400, 50, text=slide_title, font=('Arial', 18, 'bold'))
        
        # Mostrar imágenes si las hay
        if self.current_slide in self.slide_image_mapping:
            for img_info in self.slide_image_mapping[self.current_slide]:
                self.draw_image_placeholder(img_info['x'], img_info['y'], img_info['name'])
    
    def draw_image_placeholder(self, x, y, name):
        """Dibuja un placeholder para una imagen"""
        self.canvas.create_rectangle(x-50, y-30, x+50, y+30, outline='blue', width=2, dash=(5, 5))
        self.canvas.create_text(x, y, text=f"🖼️\n{name}", font=('Arial', 8), fill='blue')
    
    def show_context_menu(self, event):
        """Muestra el menú contextual"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def add_image_to_slide(self):
        """Agrega una imagen a la diapositiva actual"""
        filetypes = [
            ("Imágenes", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("Todos los archivos", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Seleccionar imagen clínica",
            filetypes=filetypes
        )
        
        if filename:
            # Agregar al mapeo
            if self.current_slide not in self.slide_image_mapping:
                self.slide_image_mapping[self.current_slide] = []
            
            img_info = {
                'path': filename,
                'name': Path(filename).name,
                'x': 400,  # Centro del canvas
                'y': 300
            }
            
            self.slide_image_mapping[self.current_slide].append(img_info)
            self.update_slide_view()
            
            messagebox.showinfo("Imagen Agregada", f"Imagen agregada a la diapositiva {self.current_slide + 1}")
    
    def remove_image_from_slide(self):
        """Elimina imagen de la diapositiva actual"""
        if self.current_slide in self.slide_image_mapping:
            if self.slide_image_mapping[self.current_slide]:
                self.slide_image_mapping[self.current_slide].pop()
                self.update_slide_view()
                messagebox.showinfo("Imagen Eliminada", "Última imagen eliminada")
    
    def on_canvas_click(self, event):
        """Maneja clic en el canvas"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
    
    def on_canvas_drag(self, event):
        """Maneja arrastre en el canvas"""
        pass  # Implementar lógica de arrastre
    
    def on_canvas_release(self, event):
        """Maneja liberación del arrastre"""
        pass  # Implementar lógica de posicionamiento
    
    def update_preview(self):
        """Actualiza la vista previa"""
        self.update_slide_view()
        messagebox.showinfo("Vista Actualizada", "Vista previa actualizada")
    
    def clear_all(self):
        """Limpia todos los campos"""
        # Limpiar datos del paciente
        for var in [self.patient_name, self.patient_age, self.patient_occupation, self.doctor_name]:
            var.set("")
        
        # Limpiar texto clínico
        self.clinical_text.delete("1.0", tk.END)
        
        # Limpiar imágenes
        self.slide_image_mapping.clear()
        self.update_slide_view()
        
        messagebox.showinfo("Limpiado", "Todos los campos han sido limpiados")
    
    def generate_report(self):
        """Genera el informe final"""
        # Validar datos
        if not self.patient_name.get().strip():
            messagebox.showerror("Error", "Debe ingresar el nombre del paciente")
            return
        
        clinical_content = self.clinical_text.get("1.0", tk.END).strip()
        if not clinical_content or "Escriba aquí el motivo" in clinical_content:
            messagebox.showerror("Error", "Debe escribir el contenido clínico")
            return
        
        # Ejecutar en hilo separado
        thread = threading.Thread(target=self._generate_report_thread)
        thread.daemon = True
        thread.start()
    
    def _generate_report_thread(self):
        """Hilo para generar el informe"""
        try:
            # Procesar texto clínico
            clinical_data = self.text_processor.process_clinical_text_direct(
                self.clinical_text.get("1.0", tk.END).strip()
            )
            
            # Recopilar imágenes
            all_images = []
            for slide_images in self.slide_image_mapping.values():
                for img_info in slide_images:
                    all_images.append(img_info['path'])
            
            # Procesar imágenes
            processed_images = []
            if all_images:
                processed_images = self.image_processor.process_multiple_images(
                    all_images,
                    remove_bg=self.remove_background.get()
                )
            
            # Preparar información del paciente
            patient_info = {
                "nombre": self.patient_name.get(),
                "edad": self.patient_age.get(),
                "sexo": self.patient_sex.get(),
                "estado_civil": self.patient_civil.get(),
                "ocupacion": self.patient_occupation.get(),
                "operador": self.doctor_name.get()
            }
            
            # Generar informe
            output_path = self.report_generator.generate_report(
                clinical_data=clinical_data,
                image_paths=processed_images,
                patient_info=patient_info
            )
            
            messagebox.showinfo("Éxito", f"Informe generado:\n{output_path}")
            
            # Preguntar si abrir
            if messagebox.askyesno("Abrir Informe", "¿Desea abrir el informe generado?"):
                import os
                os.startfile(output_path)
                
        except Exception as e:
            messagebox.showerror("Error", f"Error generando informe: {str(e)}")
    
    def run(self):
        """Ejecuta la interfaz"""
        self.root.mainloop()


def main():
    """Función principal"""
    app = VisualInterface()
    app.run()


if __name__ == "__main__":
    main()
