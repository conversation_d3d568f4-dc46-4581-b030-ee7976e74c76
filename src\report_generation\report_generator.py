"""
Generador de informes PowerPoint para informes odontológicos
"""
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
from pptx import Presentation
from pptx.util import Inches
from PIL import Image
from .template_creator import PowerPointTemplateCreator
from ..config import TEMPLATES_DIR, OUTPUT_DIR


class ClinicalReportGenerator:
    """Generador de informes clínicos que combina texto e imágenes en PowerPoint"""
    
    def __init__(self):
        self.template_creator = PowerPointTemplateCreator()
        self.template_path = TEMPLATES_DIR / "informe_odontologico_template.pptx"
        
    def ensure_template_exists(self) -> str:
        """Asegura que la plantilla existe, la crea si no"""
        if not self.template_path.exists():
            print("Creando plantilla PowerPoint...")
            return self.template_creator.create_complete_template(str(self.template_path))
        return str(self.template_path)
    
    def load_template(self) -> Presentation:
        """Carga la plantilla PowerPoint"""
        template_path = self.ensure_template_exists()
        return Presentation(template_path)
    
    def replace_text_in_slide(self, slide, replacements: Dict[str, str]) -> None:
        """Reemplaza texto en una diapositiva"""
        # Reemplazar en formas de texto
        for shape in slide.shapes:
            if hasattr(shape, "text_frame"):
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        for placeholder, value in replacements.items():
                            if placeholder in run.text:
                                run.text = run.text.replace(placeholder, str(value))
            elif hasattr(shape, "text"):
                for placeholder, value in replacements.items():
                    if placeholder in shape.text:
                        shape.text = shape.text.replace(placeholder, str(value))
    
    def replace_text_in_presentation(self, prs: Presentation, replacements: Dict[str, str]) -> None:
        """Reemplaza texto en toda la presentación"""
        for slide in prs.slides:
            self.replace_text_in_slide(slide, replacements)
    
    def add_images_to_slide(self, slide, image_paths: List[str], max_images: int = 4) -> None:
        """Agrega imágenes a la diapositiva de evidencia fotográfica"""
        # Buscar y remover placeholders de imágenes
        shapes_to_remove = []
        for shape in slide.shapes:
            if hasattr(shape, "text_frame") and "[IMAGEN_" in shape.text_frame.text:
                shapes_to_remove.append(shape)
        
        # Remover placeholders
        for shape in shapes_to_remove:
            slide.shapes._spTree.remove(shape._element)
        
        # Posiciones para las imágenes (2x2 grid)
        positions = [
            (Inches(0.5), Inches(2), Inches(4), Inches(2.5)),    # Top left
            (Inches(5), Inches(2), Inches(4), Inches(2.5)),      # Top right
            (Inches(0.5), Inches(4.8), Inches(4), Inches(2.5)),  # Bottom left
            (Inches(5), Inches(4.8), Inches(4), Inches(2.5))     # Bottom right
        ]
        
        # Agregar imágenes
        for i, image_path in enumerate(image_paths[:max_images]):
            if i >= len(positions):
                break
                
            try:
                left, top, width, height = positions[i]
                
                # Verificar que la imagen existe
                if not Path(image_path).exists():
                    print(f"Imagen no encontrada: {image_path}")
                    continue
                
                # Agregar imagen
                slide.shapes.add_picture(image_path, left, top, width, height)
                print(f"Imagen agregada: {Path(image_path).name}")
                
            except Exception as e:
                print(f"Error agregando imagen {image_path}: {e}")
                continue
    
    def find_images_slide(self, prs: Presentation) -> Optional[int]:
        """Encuentra el índice de la diapositiva de imágenes"""
        for i, slide in enumerate(prs.slides):
            for shape in slide.shapes:
                if hasattr(shape, "text_frame"):
                    text = shape.text_frame.text
                    if "EVIDENCIA CLÍNICA FOTOGRÁFICA" in text or "[IMAGEN_" in text:
                        return i
        return None
    
    def prepare_replacements(self, clinical_data: Dict, patient_info: Dict = None) -> Dict[str, str]:
        """Prepara el diccionario de reemplazos de texto"""
        replacements = {}
        
        # Información del paciente
        if patient_info:
            replacements.update({
                "[NOMBRE_PACIENTE]": patient_info.get("nombre", "No especificado"),
                "[EDAD_PACIENTE]": str(patient_info.get("edad", "No especificada")),
                "[FECHA_NACIMIENTO]": patient_info.get("fecha_nacimiento", "No especificada"),
                "[DOCUMENTO_ID]": patient_info.get("documento", "No especificado"),
                "[MEDICO_TRATANTE]": patient_info.get("medico", "No especificado"),
            })
        
        # Fecha actual
        replacements["[FECHA_CONSULTA]"] = datetime.now().strftime("%d/%m/%Y")
        
        # Datos antropométricos
        if clinical_data.get("imc_data"):
            imc_data = clinical_data["imc_data"]
            replacements.update({
                "[PESO]": str(imc_data.get("peso", "No especificado")),
                "[TALLA]": str(imc_data.get("talla", "No especificada")),
                "[IMC]": str(imc_data.get("imc", "No calculado")),
                "[CLASIFICACION_IMC]": imc_data.get("clasificacion", "No disponible")
            })
        else:
            replacements.update({
                "[PESO]": "No especificado",
                "[TALLA]": "No especificada", 
                "[IMC]": "No calculado",
                "[CLASIFICACION_IMC]": "No disponible"
            })
        
        # Secciones clínicas
        sections = clinical_data.get("sections", {})
        section_mappings = {
            "motivo de consulta": "[MOTIVO_CONSULTA]",
            "enfermedad actual": "[ENFERMEDAD_ACTUAL]",
            "antecedentes familiares": "[ANTECEDENTES_FAMILIARES]",
            "antecedentes personales": "[ANTECEDENTES_PERSONALES]",
            "antecedentes médicos": "[ANTECEDENTES_MEDICOS]",
            "antecedentes odontológicos": "[ANTECEDENTES_ODONTOLOGICOS]",
            "examen físico": "[EXAMEN_FISICO]",
            "examen extraoral": "[EXAMEN_EXTRAORAL]",
            "examen intraoral": "[EXAMEN_INTRAORAL]",
            "diagnóstico": "[DIAGNOSTICO]",
            "plan de tratamiento": "[PLAN_TRATAMIENTO]",
            "pronóstico": "[PRONOSTICO]",
            "observaciones": "[OBSERVACIONES]"
        }
        
        for section_key, placeholder in section_mappings.items():
            replacements[placeholder] = sections.get(section_key, "No especificado")
        
        # Placeholders adicionales
        replacements.update({
            "[RECOMENDACION_1]": "Seguir indicaciones médicas",
            "[RECOMENDACION_2]": "Mantener higiene oral adecuada",
            "[RECOMENDACION_3]": "Asistir a controles programados",
            "[PROXIMA_CITA]": "Por programar",
            "[FIRMA_PROFESIONAL]": "Dr. [Nombre del profesional]"
        })
        
        return replacements
    
    def generate_report(self, clinical_data: Dict, image_paths: List[str] = None, 
                       patient_info: Dict = None, output_filename: str = None) -> str:
        """Genera el informe completo"""
        
        # Cargar plantilla
        prs = self.load_template()
        
        # Preparar reemplazos de texto
        replacements = self.prepare_replacements(clinical_data, patient_info)
        
        # Reemplazar texto en toda la presentación
        self.replace_text_in_presentation(prs, replacements)
        
        # Agregar imágenes si se proporcionaron
        if image_paths:
            images_slide_index = self.find_images_slide(prs)
            if images_slide_index is not None:
                images_slide = prs.slides[images_slide_index]
                self.add_images_to_slide(images_slide, image_paths)
            else:
                print("No se encontró la diapositiva de imágenes")
        
        # Determinar nombre del archivo de salida
        if not output_filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"informe_clinico_{timestamp}.pptx"
        
        # Asegurar que termine en .pptx
        if not output_filename.endswith('.pptx'):
            output_filename += '.pptx'
        
        # Ruta completa de salida
        output_path = OUTPUT_DIR / output_filename
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Guardar presentación
        prs.save(str(output_path))
        
        print(f"Informe generado exitosamente: {output_path}")
        return str(output_path)
    
    def convert_to_pdf(self, pptx_path: str) -> str:
        """Convierte el PowerPoint a PDF (requiere instalación adicional)"""
        try:
            # Intentar conversión con comtypes (Windows)
            import comtypes.client
            
            powerpoint = comtypes.client.CreateObject("Powerpoint.Application")
            powerpoint.Visible = 1
            
            presentation = powerpoint.Presentations.Open(pptx_path)
            pdf_path = pptx_path.replace('.pptx', '.pdf')
            presentation.SaveAs(pdf_path, 32)  # 32 = PDF format
            presentation.Close()
            powerpoint.Quit()
            
            print(f"PDF generado: {pdf_path}")
            return pdf_path
            
        except ImportError:
            print("Para conversión a PDF, instale: pip install comtypes")
            return None
        except Exception as e:
            print(f"Error convirtiendo a PDF: {e}")
            return None
