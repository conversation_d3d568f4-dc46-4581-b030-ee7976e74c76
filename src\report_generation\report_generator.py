"""
Generador de informes PowerPoint para informes odontológicos
"""
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
from pptx import Presentation
from pptx.util import Inches
from PIL import Image
from .template_creator import PowerPointTemplateCreator
from ..config import TEMPLATES_DIR, OUTPUT_DIR


class ClinicalReportGenerator:
    """Generador de informes clínicos que combina texto e imágenes en PowerPoint"""

    def __init__(self):
        self.template_creator = PowerPointTemplateCreator()
        self.template_path = TEMPLATES_DIR / "plantillaejemplo.pptx"
        
    def ensure_template_exists(self) -> str:
        """Asegura que la plantilla existe"""
        if not self.template_path.exists():
            print(f"❌ Error: No se encontró la plantilla {self.template_path}")
            print("Asegúrese de que plantillaejemplo.pptx esté en el directorio templates/")
            raise FileNotFoundError(f"Plantilla no encontrada: {self.template_path}")
        return str(self.template_path)
    
    def load_template(self) -> Presentation:
        """Carga la plantilla PowerPoint"""
        template_path = self.ensure_template_exists()
        return Presentation(template_path)
    
    def replace_text_in_slide(self, slide, replacements: Dict[str, str]) -> None:
        """Reemplaza texto en una diapositiva"""
        # Reemplazar en formas de texto
        for shape in slide.shapes:
            if hasattr(shape, "text_frame"):
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        for placeholder, value in replacements.items():
                            if placeholder in run.text:
                                run.text = run.text.replace(placeholder, str(value))
            elif hasattr(shape, "text"):
                for placeholder, value in replacements.items():
                    if placeholder in shape.text:
                        shape.text = shape.text.replace(placeholder, str(value))
    
    def replace_text_in_presentation(self, prs: Presentation, replacements: Dict[str, str]) -> None:
        """Reemplaza texto en toda la presentación"""
        for slide in prs.slides:
            self.replace_text_in_slide(slide, replacements)
    
    def add_images_to_evidence_slides(self, prs: Presentation, image_paths: List[str]) -> None:
        """Agrega imágenes a las diapositivas de evidencia clínica"""
        evidence_slides = self.find_evidence_slides(prs)

        if not evidence_slides:
            print("No se encontraron diapositivas de evidencia clínica")
            return

        if not image_paths:
            print("No se proporcionaron imágenes para agregar")
            return

        print(f"Encontradas {len(evidence_slides)} diapositivas de evidencia clínica")
        print(f"Agregando {len(image_paths)} imágenes...")

        # Distribuir imágenes entre las diapositivas de evidencia
        images_per_slide = max(1, len(image_paths) // len(evidence_slides))

        for i, slide_index in enumerate(evidence_slides):
            if i * images_per_slide >= len(image_paths):
                break

            slide = prs.slides[slide_index]

            # Obtener imágenes para esta diapositiva
            start_idx = i * images_per_slide
            end_idx = min(start_idx + images_per_slide, len(image_paths))
            slide_images = image_paths[start_idx:end_idx]

            # Reemplazar la imagen existente en la diapositiva
            self.replace_slide_image(slide, slide_images[0] if slide_images else None)

    def replace_slide_image(self, slide, image_path: str) -> None:
        """Reemplaza la imagen principal en una diapositiva"""
        if not image_path or not Path(image_path).exists():
            print(f"Imagen no encontrada: {image_path}")
            return

        # Buscar la imagen existente en la diapositiva
        image_shape = None
        for shape in slide.shapes:
            if shape.shape_type == 13:  # MSO_SHAPE_TYPE.PICTURE
                image_shape = shape
                break

        if image_shape:
            # Obtener posición y tamaño de la imagen existente
            left = image_shape.left
            top = image_shape.top
            width = image_shape.width
            height = image_shape.height

            # Remover la imagen existente
            slide.shapes._spTree.remove(image_shape._element)

            try:
                # Agregar la nueva imagen en la misma posición
                slide.shapes.add_picture(image_path, left, top, width, height)
                print(f"Imagen reemplazada: {Path(image_path).name}")
            except Exception as e:
                print(f"Error reemplazando imagen: {e}")
        else:
            print("No se encontró imagen existente para reemplazar")
    
    def find_slides_by_content(self, prs: Presentation, search_text: str) -> List[int]:
        """Encuentra diapositivas que contengan texto específico"""
        matching_slides = []
        for i, slide in enumerate(prs.slides):
            for shape in slide.shapes:
                if hasattr(shape, "text_frame"):
                    for paragraph in shape.text_frame.paragraphs:
                        for run in paragraph.runs:
                            if search_text.upper() in run.text.upper():
                                matching_slides.append(i)
                                break
                elif hasattr(shape, "text") and search_text.upper() in shape.text.upper():
                    matching_slides.append(i)
                    break
        return matching_slides

    def find_patient_info_slide(self, prs: Presentation) -> Optional[int]:
        """Encuentra la diapositiva de datos personales"""
        slides = self.find_slides_by_content(prs, "DATOS PERSONALES")
        return slides[0] if slides else None

    def find_clinical_slides(self, prs: Presentation) -> Dict[str, int]:
        """Encuentra diapositivas de secciones clínicas específicas"""
        clinical_slides = {}

        # Mapeo de secciones a buscar
        sections_to_find = {
            "motivo_consulta": "MOTIVO DE CONSULTA",
            "enfermedad_actual": "ENFERMEDAD ACTUAL",
            "antecedentes": "ANTECEDENTES",
            "examen_fisico": "EXAMEN FÍSICO",
            "diagnostico": "DIAGNOSTICO",
            "tratamiento": "TRATAMIENTO"
        }

        for key, search_text in sections_to_find.items():
            slides = self.find_slides_by_content(prs, search_text)
            if slides:
                clinical_slides[key] = slides[0]

        return clinical_slides

    def find_evidence_slides(self, prs: Presentation) -> List[int]:
        """Encuentra diapositivas de evidencia clínica"""
        return self.find_slides_by_content(prs, "EVIDENCIA CLINICA")
    
    def prepare_replacements(self, clinical_data: Dict, patient_info: Dict = None) -> Dict[str, str]:
        """Prepara el diccionario de reemplazos de texto para la plantilla específica"""
        replacements = {}

        # Información del paciente - Adaptado a la plantilla existente
        if patient_info:
            # Reemplazar información en la diapositiva de datos personales
            nombre = patient_info.get("nombre", "No especificado")
            edad = patient_info.get("edad", "No especificada")
            sexo = patient_info.get("sexo", "No especificado")
            estado_civil = patient_info.get("estado_civil", "No especificado")
            ocupacion = patient_info.get("ocupacion", "No especificada")

            # Formato específico para la plantilla
            replacements.update({
                "Carmen Elena Alomias Pinchao": nombre,
                "61 años": f"{edad} años" if edad != "No especificada" else "No especificada",
                "Femenino": sexo,
                "Soltero": estado_civil,
                "Ama de casa": ocupacion,
                "Lady Sofía Henao Hoyos": patient_info.get("operador", "No especificado")
            })

        # Fecha actual
        current_date = datetime.now().strftime("%d/%m/%Y")
        
        # Datos antropométricos - Adaptado a la plantilla
        if clinical_data.get("imc_data"):
            imc_data = clinical_data["imc_data"]
            peso = imc_data.get("peso", 49.4)
            talla_cm = int(imc_data.get("talla", 1.55) * 100)  # Convertir a cm
            imc = imc_data.get("imc", "No calculado")
            clasificacion = imc_data.get("clasificacion", "No disponible")

            # Reemplazar valores específicos de la plantilla
            replacements.update({
                "49.4": str(peso),
                "155": str(talla_cm),
                "(18.5—24.9) Peso saludable": f"IMC: {imc} - {clasificacion}"
            })

        # Secciones clínicas - Reemplazar contenido específico
        sections = clinical_data.get("sections", {})

        # Motivo de consulta
        if "motivo de consulta" in sections:
            motivo = sections["motivo de consulta"]
            replacements['"Quiero que me cambien las resinas en los cuellos de mis dientes y tengo un diente con un hueco"'] = f'"{motivo}"'

        # Enfermedad actual
        if "enfermedad actual" in sections:
            enfermedad = sections["enfermedad actual"]
            replacements["'Paciente menciona molestias en el color de las restauraciones ubicadas en el tercio cervical coronal y fractura dental'"] = f"'{enfermedad}'"
        
        # Antecedentes familiares
        if "antecedentes familiares" in sections:
            antecedentes_fam = sections["antecedentes familiares"]
            replacements["Osteoporosis-Madre"] = antecedentes_fam

        # Antecedentes médicos
        if "antecedentes médicos" in sections:
            antecedentes_med = sections["antecedentes médicos"]
            # Reemplazar el texto largo de antecedentes médicos
            original_text = "Escoliosis lumbar: Diagnostico 52 años. Presenta dolor de espada constantemente.\nOstopenia: Diagnosticaron el abril 2023 y le realizaron cirugía en rodilla derecha (meniscos) en febrero del 2022.\nHipotiroidismo:  Diagnostico 55 años. Exámenes de control estables.\nEstrabismo: Estrabismo en ojo izquierdo diagnosticado a los dos años. Operación de estrabismo a los 8 y 9 años."
            replacements[original_text] = antecedentes_med

        # Antecedentes odontológicos
        if "antecedentes odontológicos" in sections:
            antecedentes_odont = sections["antecedentes odontológicos"]
            original_odont = "Consume bebidas acidas y frías todos los días.\n\nPaciente desconoce técnicas de cepillado. Por lo tanto, su forma de cepillado es con movimientos horizontal.\n\nUsa cepillo con filamentos gruesos.\nExtracción de diente 26 por fractura y 18,28,38 y 48."
            replacements[original_odont] = antecedentes_odont

        # Diagnóstico
        if "diagnóstico" in sections:
            diagnostico = sections["diagnóstico"]
            original_diag = "DIAGNÓSTICO SISTÉMICO: No presenta riesgo de gastritis en la actualidad. Signos vitales y peso en normalidad según los estándares establecidos.\n\nDIAGNÓSTICO MUSCULAR: No presenta dolor al tacto ni en movimiento.\n\nDIAGNÓSTICO ATM:  Funcionamiento normal. Sin artralgia. No presentó crepitación ni clic en ATM.\nDIAGNÓSTICO ENDODONTICO: Pulpitis irreversible asintomátic\nDIAGNÓSTICO PERIODONTAL: Deformidad mucogingival.\n\nDIAGNOSTICO OCLUSAL: Maloclusión funcional, maloclusión anatómica.\n.\nDIAGNÓSTICO DENTAL: Lesión cariosa activa ICDAD 5 en tercio cervical con extensión yuxtagingival en el diente 33."
            replacements[original_diag] = diagnostico

        # Plan de tratamiento
        if "plan de tratamiento" in sections:
            tratamiento = sections["plan de tratamiento"]
            original_trat = "TX PERIODONTAL:\n Instruir al paciente con técnicas de cepillado adecuadas para disminuir la fuerza mecánica aplicada diariamente.\nRemitir con periodoncia para valorar injerto mucogingival.\n\nTX OCLUSAL:\nCorregir facetas de desgaste en los dientes en movimientos de protrusión\n\nTX DENTAL:\nIniciar la fase higiénica en el paciente y operatoria.\nAplicar barniz de fluor para potencializar la desmineralización del esmalte.\nCambiar cepillo de cerdas dura por un cepillo de cerdas suaves, menos abrasivas y uso de hilo dental 3 veces al día.\nRetirar el material cariado y resinas desadaptas."
            replacements[original_trat] = tratamiento

        return replacements
    
    def generate_report(self, clinical_data: Dict, image_paths: List[str] = None,
                       patient_info: Dict = None, output_filename: str = None) -> str:
        """Genera el informe completo usando la plantilla específica"""

        print("🔄 Cargando plantilla personalizada...")
        # Cargar plantilla
        prs = self.load_template()

        print("📝 Preparando reemplazos de texto...")
        # Preparar reemplazos de texto
        replacements = self.prepare_replacements(clinical_data, patient_info)

        print(f"🔄 Reemplazando texto en {len(prs.slides)} diapositivas...")
        # Reemplazar texto en toda la presentación
        self.replace_text_in_presentation(prs, replacements)

        # Agregar imágenes si se proporcionaron
        if image_paths:
            print(f"🖼️  Agregando {len(image_paths)} imágenes a diapositivas de evidencia...")
            self.add_images_to_evidence_slides(prs, image_paths)

        # Determinar nombre del archivo de salida
        if not output_filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            patient_name = patient_info.get("nombre", "Paciente") if patient_info else "Paciente"
            # Limpiar nombre para archivo
            clean_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"informe_{clean_name}_{timestamp}.pptx"

        # Asegurar que termine en .pptx
        if not output_filename.endswith('.pptx'):
            output_filename += '.pptx'

        # Ruta completa de salida
        output_path = OUTPUT_DIR / output_filename
        output_path.parent.mkdir(parents=True, exist_ok=True)

        print("💾 Guardando informe...")
        # Guardar presentación
        prs.save(str(output_path))

        print(f"✅ Informe generado exitosamente: {output_path}")
        return str(output_path)
    
    def convert_to_pdf(self, pptx_path: str) -> str:
        """Convierte el PowerPoint a PDF (requiere instalación adicional)"""
        try:
            # Intentar conversión con comtypes (Windows)
            import comtypes.client
            
            powerpoint = comtypes.client.CreateObject("Powerpoint.Application")
            powerpoint.Visible = 1
            
            presentation = powerpoint.Presentations.Open(pptx_path)
            pdf_path = pptx_path.replace('.pptx', '.pdf')
            presentation.SaveAs(pdf_path, 32)  # 32 = PDF format
            presentation.Close()
            powerpoint.Quit()
            
            print(f"PDF generado: {pdf_path}")
            return pdf_path
            
        except ImportError:
            print("Para conversión a PDF, instale: pip install comtypes")
            return None
        except Exception as e:
            print(f"Error convirtiendo a PDF: {e}")
            return None
