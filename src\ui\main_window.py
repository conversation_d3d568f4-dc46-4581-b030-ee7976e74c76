"""
Interfaz gráfica principal para Infología
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from typing import List, Optional
import threading
from ..text_processing.clinical_text_processor import ClinicalTextProcessor
from ..image_processing.image_processor import ClinicalImageProcessor
from ..report_generation.report_generator import ClinicalReportGenerator


class InfologiaMainWindow:
    """Ventana principal de la aplicación Infología"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Infología - Generador de Informes Clínicos Odontológicos")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Procesadores
        self.text_processor = ClinicalTextProcessor()
        self.image_processor = ClinicalImageProcessor()
        self.report_generator = ClinicalReportGenerator()
        
        # Variables
        self.text_file_path = tk.StringVar()
        self.image_paths = []
        self.output_filename = tk.StringVar()
        self.remove_background = tk.BooleanVar(value=True)
        self.generate_pdf = tk.BooleanVar(value=False)
        
        # Variables de información del paciente
        self.patient_name = tk.StringVar()
        self.patient_age = tk.StringVar()
        self.patient_birth_date = tk.StringVar()
        self.patient_document = tk.StringVar()
        self.doctor_name = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        """Configura la interfaz de usuario"""
        # Estilo
        style = ttk.Style()
        style.theme_use('clam')
        
        # Frame principal con scroll
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Título
        title_label = ttk.Label(main_frame, text="Infología - Generador de Informes Clínicos", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Notebook para pestañas
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Pestaña 1: Archivos de entrada
        files_frame = ttk.Frame(notebook)
        notebook.add(files_frame, text="Archivos de Entrada")
        self.setup_files_tab(files_frame)
        
        # Pestaña 2: Información del paciente
        patient_frame = ttk.Frame(notebook)
        notebook.add(patient_frame, text="Información del Paciente")
        self.setup_patient_tab(patient_frame)
        
        # Pestaña 3: Configuración
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuración")
        self.setup_config_tab(config_frame)
        
        # Frame de botones
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Botón generar informe
        generate_btn = ttk.Button(buttons_frame, text="Generar Informe", 
                                 command=self.generate_report, style='Accent.TButton')
        generate_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Botón limpiar
        clear_btn = ttk.Button(buttons_frame, text="Limpiar Todo", 
                              command=self.clear_all)
        clear_btn.pack(side=tk.RIGHT)
        
        # Barra de progreso
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(10, 0))
        
        # Área de estado
        self.status_text = tk.Text(main_frame, height=6, wrap=tk.WORD)
        self.status_text.pack(fill=tk.X, pady=(10, 0))
        
        # Scrollbar para el área de estado
        status_scroll = ttk.Scrollbar(self.status_text)
        status_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.status_text.config(yscrollcommand=status_scroll.set)
        status_scroll.config(command=self.status_text.yview)
        
    def setup_files_tab(self, parent):
        """Configura la pestaña de archivos"""
        # Archivo de texto clínico
        text_group = ttk.LabelFrame(parent, text="Archivo de Texto Clínico", padding=10)
        text_group.pack(fill=tk.X, pady=(0, 10))
        
        text_frame = ttk.Frame(text_group)
        text_frame.pack(fill=tk.X)
        
        ttk.Entry(text_frame, textvariable=self.text_file_path, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(text_frame, text="Seleccionar", command=self.select_text_file).pack(side=tk.RIGHT, padx=(10, 0))
        
        ttk.Label(text_group, text="Formatos soportados: .txt, .docx", 
                 font=('Arial', 8), foreground='gray').pack(anchor=tk.W, pady=(5, 0))
        
        # Imágenes clínicas
        images_group = ttk.LabelFrame(parent, text="Imágenes Clínicas", padding=10)
        images_group.pack(fill=tk.BOTH, expand=True)
        
        images_buttons_frame = ttk.Frame(images_group)
        images_buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(images_buttons_frame, text="Agregar Imágenes", 
                  command=self.select_images).pack(side=tk.LEFT)
        ttk.Button(images_buttons_frame, text="Limpiar Lista", 
                  command=self.clear_images).pack(side=tk.LEFT, padx=(10, 0))
        
        # Lista de imágenes
        self.images_listbox = tk.Listbox(images_group, height=8)
        self.images_listbox.pack(fill=tk.BOTH, expand=True)
        
        images_scroll = ttk.Scrollbar(self.images_listbox)
        images_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.images_listbox.config(yscrollcommand=images_scroll.set)
        images_scroll.config(command=self.images_listbox.yview)
        
        ttk.Label(images_group, text="Formatos soportados: .jpg, .jpeg, .png, .bmp, .tiff", 
                 font=('Arial', 8), foreground='gray').pack(anchor=tk.W, pady=(5, 0))
    
    def setup_patient_tab(self, parent):
        """Configura la pestaña de información del paciente"""
        # Frame con scroll
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Información básica
        basic_group = ttk.LabelFrame(scrollable_frame, text="Información Básica", padding=10)
        basic_group.pack(fill=tk.X, pady=(0, 10))
        
        # Campos del paciente
        fields = [
            ("Nombre completo:", self.patient_name),
            ("Edad:", self.patient_age),
            ("Fecha de nacimiento (DD/MM/AAAA):", self.patient_birth_date),
            ("Documento de identidad:", self.patient_document),
            ("Médico tratante:", self.doctor_name)
        ]
        
        for i, (label_text, var) in enumerate(fields):
            row_frame = ttk.Frame(basic_group)
            row_frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(row_frame, text=label_text, width=30).pack(side=tk.LEFT)
            ttk.Entry(row_frame, textvariable=var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def setup_config_tab(self, parent):
        """Configura la pestaña de configuración"""
        # Procesamiento de imágenes
        image_group = ttk.LabelFrame(parent, text="Procesamiento de Imágenes", padding=10)
        image_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(image_group, text="Remover fondo automáticamente", 
                       variable=self.remove_background).pack(anchor=tk.W)
        
        # Salida
        output_group = ttk.LabelFrame(parent, text="Archivo de Salida", padding=10)
        output_group.pack(fill=tk.X, pady=(0, 10))
        
        output_frame = ttk.Frame(output_group)
        output_frame.pack(fill=tk.X)
        
        ttk.Label(output_frame, text="Nombre del archivo:").pack(side=tk.LEFT)
        ttk.Entry(output_frame, textvariable=self.output_filename, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        ttk.Label(output_group, text="Dejar vacío para generar nombre automático", 
                 font=('Arial', 8), foreground='gray').pack(anchor=tk.W, pady=(5, 0))
        
        ttk.Checkbutton(output_group, text="Generar también archivo PDF", 
                       variable=self.generate_pdf).pack(anchor=tk.W, pady=(10, 0))
    
    def select_text_file(self):
        """Selecciona archivo de texto clínico"""
        filetypes = [
            ("Archivos de texto", "*.txt *.docx"),
            ("Archivos de texto plano", "*.txt"),
            ("Documentos Word", "*.docx"),
            ("Todos los archivos", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Seleccionar archivo de texto clínico",
            filetypes=filetypes
        )
        
        if filename:
            self.text_file_path.set(filename)
            self.log_message(f"Archivo de texto seleccionado: {Path(filename).name}")
    
    def select_images(self):
        """Selecciona imágenes clínicas"""
        filetypes = [
            ("Imágenes", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG", "*.jpg *.jpeg"),
            ("PNG", "*.png"),
            ("Todos los archivos", "*.*")
        ]
        
        filenames = filedialog.askopenfilenames(
            title="Seleccionar imágenes clínicas",
            filetypes=filetypes
        )
        
        if filenames:
            for filename in filenames:
                if filename not in self.image_paths:
                    self.image_paths.append(filename)
                    self.images_listbox.insert(tk.END, Path(filename).name)
            
            self.log_message(f"Agregadas {len(filenames)} imágenes")
    
    def clear_images(self):
        """Limpia la lista de imágenes"""
        self.image_paths.clear()
        self.images_listbox.delete(0, tk.END)
        self.log_message("Lista de imágenes limpiada")
    
    def clear_all(self):
        """Limpia todos los campos"""
        self.text_file_path.set("")
        self.clear_images()
        self.output_filename.set("")
        
        # Limpiar información del paciente
        for var in [self.patient_name, self.patient_age, self.patient_birth_date, 
                   self.patient_document, self.doctor_name]:
            var.set("")
        
        self.status_text.delete(1.0, tk.END)
        self.log_message("Todos los campos limpiados")
    
    def log_message(self, message: str):
        """Agrega mensaje al área de estado"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def validate_inputs(self) -> bool:
        """Valida las entradas del usuario"""
        if not self.text_file_path.get():
            messagebox.showerror("Error", "Debe seleccionar un archivo de texto clínico")
            return False
        
        if not Path(self.text_file_path.get()).exists():
            messagebox.showerror("Error", "El archivo de texto seleccionado no existe")
            return False
        
        return True
    
    def generate_report(self):
        """Genera el informe en un hilo separado"""
        if not self.validate_inputs():
            return
        
        # Ejecutar en hilo separado para no bloquear la UI
        thread = threading.Thread(target=self._generate_report_thread)
        thread.daemon = True
        thread.start()
    
    def _generate_report_thread(self):
        """Hilo para generar el informe"""
        try:
            self.progress.start()
            self.log_message("Iniciando generación del informe...")
            
            # Procesar texto clínico
            self.log_message("Procesando texto clínico...")
            clinical_data = self.text_processor.process_clinical_text(self.text_file_path.get())
            
            # Procesar imágenes si existen
            processed_images = []
            if self.image_paths:
                self.log_message(f"Procesando {len(self.image_paths)} imágenes...")
                processed_images = self.image_processor.process_multiple_images(
                    self.image_paths,
                    remove_bg=self.remove_background.get()
                )
            
            # Preparar información del paciente
            patient_info = {
                "nombre": self.patient_name.get() or "No especificado",
                "edad": self.patient_age.get() or "No especificada",
                "fecha_nacimiento": self.patient_birth_date.get() or "No especificada",
                "documento": self.patient_document.get() or "No especificado",
                "medico": self.doctor_name.get() or "No especificado"
            }
            
            # Generar informe
            self.log_message("Generando informe PowerPoint...")
            output_file = self.report_generator.generate_report(
                clinical_data=clinical_data,
                image_paths=processed_images,
                patient_info=patient_info,
                output_filename=self.output_filename.get() or None
            )
            
            # Generar PDF si se solicita
            if self.generate_pdf.get():
                self.log_message("Generando archivo PDF...")
                pdf_file = self.report_generator.convert_to_pdf(output_file)
                if pdf_file:
                    self.log_message(f"PDF generado: {Path(pdf_file).name}")
            
            self.log_message(f"¡Informe generado exitosamente!")
            self.log_message(f"Archivo: {Path(output_file).name}")
            
            # Mostrar mensaje de éxito
            self.root.after(0, lambda: messagebox.showinfo(
                "Éxito", 
                f"Informe generado exitosamente:\n{output_file}"
            ))
            
        except Exception as e:
            error_msg = f"Error generando informe: {str(e)}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
        
        finally:
            self.progress.stop()
    
    def run(self):
        """Ejecuta la aplicación"""
        self.root.mainloop()
