#!/usr/bin/env python3
"""
Script de pruebas para Infología
"""
import sys
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

# Agregar src al path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.text_processing.clinical_text_processor import ClinicalTextProcessor
from src.image_processing.image_processor import ClinicalImageProcessor
from src.report_generation.report_generator import ClinicalReportGenerator
from src.report_generation.template_creator import PowerPointTemplateCreator


class TestClinicalTextProcessor(unittest.TestCase):
    """Pruebas para el procesador de texto clínico"""
    
    def setUp(self):
        self.processor = ClinicalTextProcessor()
    
    def test_identify_sections(self):
        """Prueba la identificación de secciones"""
        text = """
        Motivo de consulta:
        Dolor en molar superior
        
        Enfermedad actual:
        Dolor intenso desde hace 3 días
        
        Examen físico:
        Peso: 75 kg
        Talla: 1.75 m
        """
        
        sections = self.processor.identify_sections(text)
        
        self.assertIn("motivo de consulta", sections)
        self.assertIn("enfermedad actual", sections)
        self.assertIn("examen físico", sections)
        self.assertEqual(sections["motivo de consulta"], "Dolor en molar superior")
    
    def test_extract_measurements(self):
        """Prueba la extracción de medidas"""
        text = "Peso: 75 kg, Talla: 1.75 m"
        measurements = self.processor.extract_measurements(text)
        
        self.assertEqual(measurements["peso"], 75.0)
        self.assertEqual(measurements["talla"], 1.75)
    
    def test_calculate_imc(self):
        """Prueba el cálculo de IMC"""
        imc, classification = self.processor.calculate_imc(75, 1.75)
        
        self.assertAlmostEqual(imc, 24.49, places=2)
        self.assertEqual(classification, "Normal")
    
    def test_calculate_imc_obesity(self):
        """Prueba clasificación de obesidad"""
        imc, classification = self.processor.calculate_imc(100, 1.75)
        
        self.assertAlmostEqual(imc, 32.65, places=2)
        self.assertEqual(classification, "Obesidad Grado I")


class TestClinicalImageProcessor(unittest.TestCase):
    """Pruebas para el procesador de imágenes"""
    
    def setUp(self):
        self.processor = ClinicalImageProcessor()
    
    def test_is_supported_format(self):
        """Prueba formatos soportados"""
        self.assertTrue(self.processor.is_supported_format("test.jpg"))
        self.assertTrue(self.processor.is_supported_format("test.png"))
        self.assertFalse(self.processor.is_supported_format("test.gif"))
    
    @patch('PIL.Image.open')
    def test_load_image(self, mock_open):
        """Prueba carga de imagen"""
        mock_image = MagicMock()
        mock_open.return_value = mock_image
        
        # Simular que el archivo existe
        with patch('pathlib.Path.exists', return_value=True):
            result = self.processor.load_image("test.jpg")
            mock_open.assert_called_once()


class TestPowerPointTemplateCreator(unittest.TestCase):
    """Pruebas para el creador de plantillas"""
    
    def setUp(self):
        self.creator = PowerPointTemplateCreator()
    
    def test_create_base_template(self):
        """Prueba creación de plantilla base"""
        prs = self.creator.create_base_template()
        
        self.assertIsNotNone(prs)
        self.assertEqual(prs.slide_width, self.creator.slide_width)
        self.assertEqual(prs.slide_height, self.creator.slide_height)


class TestClinicalReportGenerator(unittest.TestCase):
    """Pruebas para el generador de informes"""
    
    def setUp(self):
        self.generator = ClinicalReportGenerator()
    
    def test_prepare_replacements(self):
        """Prueba preparación de reemplazos"""
        clinical_data = {
            "sections": {
                "motivo de consulta": "Dolor dental",
                "diagnóstico": "Caries profunda"
            },
            "imc_data": {
                "peso": 75,
                "talla": 1.75,
                "imc": 24.49,
                "clasificacion": "Normal"
            }
        }
        
        patient_info = {
            "nombre": "Juan Pérez",
            "edad": "35"
        }
        
        replacements = self.generator.prepare_replacements(clinical_data, patient_info)
        
        self.assertEqual(replacements["[NOMBRE_PACIENTE]"], "Juan Pérez")
        self.assertEqual(replacements["[EDAD_PACIENTE]"], "35")
        self.assertEqual(replacements["[MOTIVO_CONSULTA]"], "Dolor dental")
        self.assertEqual(replacements["[DIAGNOSTICO]"], "Caries profunda")
        self.assertEqual(replacements["[IMC]"], "24.49")


class TestIntegration(unittest.TestCase):
    """Pruebas de integración"""
    
    def test_full_workflow_with_example(self):
        """Prueba el flujo completo con el archivo de ejemplo"""
        example_file = Path("examples/caso_clinico_ejemplo.txt")
        
        if not example_file.exists():
            self.skipTest("Archivo de ejemplo no encontrado")
        
        # Procesar texto
        text_processor = ClinicalTextProcessor()
        clinical_data = text_processor.process_clinical_text(str(example_file))
        
        # Verificar que se procesó correctamente
        self.assertIsInstance(clinical_data, dict)
        self.assertIn("sections", clinical_data)
        self.assertGreater(len(clinical_data["sections"]), 0)
        
        # Verificar cálculo de IMC
        if clinical_data.get("imc_data"):
            self.assertIn("imc", clinical_data["imc_data"])
            self.assertIn("clasificacion", clinical_data["imc_data"])
        
        print(f"✅ Procesamiento exitoso: {len(clinical_data['sections'])} secciones encontradas")


def run_performance_test():
    """Ejecuta pruebas de rendimiento básicas"""
    print("\n🚀 Ejecutando pruebas de rendimiento...")
    
    import time
    
    # Prueba de procesamiento de texto
    example_file = Path("examples/caso_clinico_ejemplo.txt")
    if example_file.exists():
        processor = ClinicalTextProcessor()
        
        start_time = time.time()
        clinical_data = processor.process_clinical_text(str(example_file))
        end_time = time.time()
        
        print(f"⏱️  Procesamiento de texto: {end_time - start_time:.3f} segundos")
        print(f"📊 Secciones procesadas: {len(clinical_data['sections'])}")


def main():
    """Ejecuta todas las pruebas"""
    print("🧪 Infología - Suite de Pruebas")
    print("=" * 50)
    
    # Ejecutar pruebas unitarias
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Agregar todas las clases de prueba
    test_classes = [
        TestClinicalTextProcessor,
        TestClinicalImageProcessor,
        TestPowerPointTemplateCreator,
        TestClinicalReportGenerator,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Ejecutar pruebas
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Ejecutar pruebas de rendimiento
    run_performance_test()
    
    # Resumen
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 ¡Todas las pruebas pasaron exitosamente!")
        print(f"✅ Pruebas ejecutadas: {result.testsRun}")
    else:
        print("❌ Algunas pruebas fallaron")
        print(f"❌ Errores: {len(result.errors)}")
        print(f"❌ Fallos: {len(result.failures)}")
        
        # Mostrar detalles de errores
        for test, error in result.errors:
            print(f"\n🔴 Error en {test}:")
            print(error)
        
        for test, failure in result.failures:
            print(f"\n🔴 Fallo en {test}:")
            print(failure)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
