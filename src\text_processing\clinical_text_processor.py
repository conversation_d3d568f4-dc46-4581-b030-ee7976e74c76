"""
Procesador de texto clínico para informes odontológicos
"""
import re
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from docx import Document
from ..config import TEXT_CONFIG, IMC_CLASSIFICATION


class ClinicalTextProcessor:
    """Procesador de texto clínico que identifica secciones y calcula métricas"""
    
    def __init__(self):
        self.sections = TEXT_CONFIG["sections"]
        self.supported_formats = TEXT_CONFIG["supported_formats"]
        
    def read_file(self, file_path: str) -> str:
        """Lee un archivo de texto o docx y retorna su contenido"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"El archivo {file_path} no existe")
            
        if file_path.suffix.lower() not in self.supported_formats:
            raise ValueError(f"Formato no soportado: {file_path.suffix}")
            
        if file_path.suffix.lower() == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        elif file_path.suffix.lower() == '.docx':
            doc = Document(file_path)
            return '\n'.join([paragraph.text for paragraph in doc.paragraphs])
    
    def identify_sections(self, text: str) -> Dict[str, str]:
        """Identifica automáticamente las secciones clínicas en el texto"""
        sections_found = {}
        text_lower = text.lower()
        
        # Dividir el texto en líneas para mejor procesamiento
        lines = text.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            line_lower = line.lower().strip()
            
            # Buscar si la línea contiene alguna sección conocida
            section_found = None
            for section in self.sections:
                if section in line_lower:
                    # Verificar que sea realmente un encabezado (no parte de una oración)
                    if self._is_section_header(line_lower, section):
                        section_found = section
                        break
            
            if section_found:
                # Guardar la sección anterior si existe
                if current_section and current_content:
                    content = '\n'.join(current_content).strip()
                    if content:  # Solo agregar si tiene contenido
                        sections_found[current_section] = content
                
                # Iniciar nueva sección
                current_section = section_found
                current_content = []
            elif current_section:
                # Agregar contenido a la sección actual
                if line.strip():  # Solo agregar líneas no vacías
                    current_content.append(line.strip())
        
        # Agregar la última sección
        if current_section and current_content:
            content = '\n'.join(current_content).strip()
            if content:
                sections_found[current_section] = content
        
        return sections_found
    
    def _is_section_header(self, line: str, section: str) -> bool:
        """Determina si una línea es realmente un encabezado de sección"""
        # Patrones que indican que es un encabezado
        header_patterns = [
            f"^{re.escape(section)}:?$",  # Sección sola en la línea
            f"^{re.escape(section)}:.*",  # Sección seguida de dos puntos
            f"^\d+\.?\s*{re.escape(section)}:?",  # Numerada
            f"^-\s*{re.escape(section)}:?",  # Con guión
            f"^\*\s*{re.escape(section)}:?",  # Con asterisco
        ]
        
        for pattern in header_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                return True
        
        return False
    
    def extract_measurements(self, text: str) -> Dict[str, Optional[float]]:
        """Extrae peso y talla del texto"""
        measurements = {"peso": None, "talla": None}
        
        # Patrones para peso (kg)
        peso_patterns = [
            r"peso:?\s*(\d+(?:\.\d+)?)\s*kg",
            r"peso:?\s*(\d+(?:\.\d+)?)",
            r"(\d+(?:\.\d+)?)\s*kg",
        ]
        
        # Patrones para talla (m o cm)
        talla_patterns = [
            r"talla:?\s*(\d+(?:\.\d+)?)\s*m(?:\s|$)",
            r"altura:?\s*(\d+(?:\.\d+)?)\s*m(?:\s|$)",
            r"talla:?\s*(\d+)\s*cm",
            r"altura:?\s*(\d+)\s*cm",
        ]
        
        text_lower = text.lower()
        
        # Buscar peso
        for pattern in peso_patterns:
            match = re.search(pattern, text_lower)
            if match:
                measurements["peso"] = float(match.group(1))
                break
        
        # Buscar talla
        for pattern in talla_patterns:
            match = re.search(pattern, text_lower)
            if match:
                value = float(match.group(1))
                # Convertir cm a m si es necesario
                if "cm" in pattern:
                    value = value / 100
                measurements["talla"] = value
                break
        
        return measurements
    
    def calculate_imc(self, peso: float, talla: float) -> Tuple[float, str]:
        """Calcula el IMC y su clasificación según la OMS"""
        if peso <= 0 or talla <= 0:
            raise ValueError("Peso y talla deben ser valores positivos")
        
        imc = peso / (talla ** 2)
        
        # Clasificar según OMS
        for classification, (min_val, max_val) in IMC_CLASSIFICATION.items():
            if min_val <= imc < max_val:
                return round(imc, 2), classification.replace("_", " ").title()
        
        return round(imc, 2), "Clasificación no disponible"
    
    def process_clinical_text(self, file_path: str) -> Dict:
        """Procesa completamente un archivo de texto clínico"""
        # Leer archivo
        text = self.read_file(file_path)
        
        # Identificar secciones
        sections = self.identify_sections(text)
        
        # Extraer medidas y calcular IMC si es posible
        measurements = self.extract_measurements(text)
        imc_data = None
        
        if measurements["peso"] and measurements["talla"]:
            try:
                imc, classification = self.calculate_imc(
                    measurements["peso"], 
                    measurements["talla"]
                )
                imc_data = {
                    "peso": measurements["peso"],
                    "talla": measurements["talla"],
                    "imc": imc,
                    "clasificacion": classification
                }
            except ValueError as e:
                print(f"Error calculando IMC: {e}")
        
        return {
            "sections": sections,
            "measurements": measurements,
            "imc_data": imc_data,
            "original_text": text
        }

    def process_clinical_text_direct(self, text: str) -> Dict:
        """Procesa texto clínico directo (sin archivo) y extrae información estructurada"""
        # Identificar secciones
        sections = self.identify_sections(text)

        # Extraer medidas y calcular IMC si es posible
        measurements = self.extract_measurements(text)
        imc_data = None

        if measurements["peso"] and measurements["talla"]:
            try:
                imc, classification = self.calculate_imc(
                    measurements["peso"],
                    measurements["talla"]
                )
                imc_data = {
                    "peso": measurements["peso"],
                    "talla": measurements["talla"],
                    "imc": imc,
                    "clasificacion": classification
                }
            except ValueError as e:
                print(f"Error calculando IMC: {e}")

        return {
            "sections": sections,
            "measurements": measurements,
            "imc_data": imc_data,
            "original_text": text
        }
