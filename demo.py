#!/usr/bin/env python3
"""
Demostración de Infología - Sistema de Automatización de Informes Clínicos
"""
import sys
from pathlib import Path

# Agregar src al path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.text_processing.clinical_text_processor import ClinicalTextProcessor
from src.image_processing.image_processor import ClinicalImageProcessor
from src.report_generation.report_generator import ClinicalReportGenerator


def demo_text_processing():
    """Demostración del procesamiento de texto"""
    print("📝 DEMOSTRACIÓN: Procesamiento de Texto Clínico")
    print("-" * 50)
    
    processor = ClinicalTextProcessor()
    example_file = Path("examples/caso_clinico_ejemplo.txt")
    
    if not example_file.exists():
        print("❌ Archivo de ejemplo no encontrado")
        return
    
    print(f"📄 Procesando: {example_file.name}")
    
    # Procesar el archivo
    clinical_data = processor.process_clinical_text(str(example_file))
    
    # Mostrar secciones encontradas
    print(f"\n🔍 Secciones identificadas: {len(clinical_data['sections'])}")
    for section, content in clinical_data['sections'].items():
        print(f"  • {section.title()}: {content[:50]}...")
    
    # Mostrar datos de IMC si están disponibles
    if clinical_data['imc_data']:
        imc_data = clinical_data['imc_data']
        print(f"\n📊 Datos antropométricos:")
        print(f"  • Peso: {imc_data['peso']} kg")
        print(f"  • Talla: {imc_data['talla']} m")
        print(f"  • IMC: {imc_data['imc']} ({imc_data['clasificacion']})")
    
    print("\n✅ Procesamiento de texto completado")
    return clinical_data


def demo_image_processing():
    """Demostración del procesamiento de imágenes"""
    print("\n🖼️  DEMOSTRACIÓN: Procesamiento de Imágenes")
    print("-" * 50)
    
    processor = ClinicalImageProcessor()
    
    # Buscar imágenes de ejemplo en el directorio data
    data_dir = Path("data")
    image_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tiff"]
    
    image_files = []
    for ext in image_extensions:
        image_files.extend(data_dir.glob(f"*{ext}"))
        image_files.extend(data_dir.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print("ℹ️  No se encontraron imágenes en el directorio 'data'")
        print("   Para probar esta funcionalidad, coloque imágenes en la carpeta 'data/'")
        return []
    
    print(f"🔍 Encontradas {len(image_files)} imágenes:")
    for img in image_files:
        print(f"  • {img.name}")
    
    # Procesar las primeras 4 imágenes (máximo para el informe)
    images_to_process = image_files[:4]
    
    try:
        print(f"\n🔄 Procesando {len(images_to_process)} imágenes...")
        processed_images = processor.process_multiple_images(
            [str(img) for img in images_to_process],
            output_dir="output/processed_images",
            remove_bg=True,
            crop=True,
            resize=True,
            enhance=True
        )
        
        print(f"✅ Procesamiento completado: {len(processed_images)} imágenes")
        return processed_images
        
    except Exception as e:
        print(f"❌ Error procesando imágenes: {e}")
        print("   Nota: La primera ejecución puede tardar más debido a la descarga de modelos de IA")
        return []


def demo_report_generation(clinical_data, processed_images):
    """Demostración de la generación de informes"""
    print("\n📋 DEMOSTRACIÓN: Generación de Informes")
    print("-" * 50)
    
    generator = ClinicalReportGenerator()
    
    # Información de ejemplo del paciente
    patient_info = {
        "nombre": "Juan Pérez García",
        "edad": "35",
        "fecha_nacimiento": "15/03/1988",
        "documento": "12345678",
        "medico": "Dr. María González"
    }
    
    print("👤 Información del paciente:")
    for key, value in patient_info.items():
        print(f"  • {key.title()}: {value}")
    
    try:
        print(f"\n🔄 Generando informe PowerPoint...")
        
        output_file = generator.generate_report(
            clinical_data=clinical_data,
            image_paths=processed_images,
            patient_info=patient_info,
            output_filename="demo_informe_clinico.pptx"
        )
        
        print(f"✅ Informe generado exitosamente:")
        print(f"   📄 {output_file}")
        
        # Intentar generar PDF también
        print(f"\n🔄 Intentando generar PDF...")
        pdf_file = generator.convert_to_pdf(output_file)
        
        if pdf_file:
            print(f"✅ PDF generado: {pdf_file}")
        else:
            print("ℹ️  PDF no generado (requiere Microsoft PowerPoint en Windows)")
        
        return output_file
        
    except Exception as e:
        print(f"❌ Error generando informe: {e}")
        return None


def demo_complete_workflow():
    """Demostración del flujo completo"""
    print("🏥 INFOLOGÍA - DEMOSTRACIÓN COMPLETA")
    print("=" * 60)
    print("Sistema de Automatización de Informes Clínicos Odontológicos")
    print("=" * 60)
    
    # Paso 1: Procesamiento de texto
    clinical_data = demo_text_processing()
    if not clinical_data:
        print("❌ No se pudo procesar el texto clínico")
        return
    
    # Paso 2: Procesamiento de imágenes
    processed_images = demo_image_processing()
    
    # Paso 3: Generación de informe
    output_file = demo_report_generation(clinical_data, processed_images)
    
    # Resumen final
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE LA DEMOSTRACIÓN")
    print("=" * 60)
    
    print(f"✅ Secciones clínicas procesadas: {len(clinical_data['sections'])}")
    print(f"✅ Imágenes procesadas: {len(processed_images)}")
    
    if clinical_data.get('imc_data'):
        print(f"✅ IMC calculado: {clinical_data['imc_data']['imc']}")
    
    if output_file:
        print(f"✅ Informe generado: {Path(output_file).name}")
        print(f"\n📁 Ubicación: {output_file}")
        
        # Instrucciones para abrir el archivo
        print(f"\n💡 Para ver el informe:")
        print(f"   • Abrir: {output_file}")
        print(f"   • O ejecutar: start \"{output_file}\" (Windows)")
    else:
        print("❌ No se pudo generar el informe")
    
    print(f"\n🎉 ¡Demostración completada!")
    print(f"   Para usar la aplicación completa, ejecute: python main.py")


def main():
    """Función principal de la demostración"""
    try:
        demo_complete_workflow()
    except KeyboardInterrupt:
        print("\n\n⏹️  Demostración interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error en la demostración: {e}")
        print("   Asegúrese de haber ejecutado 'python setup.py' primero")


if __name__ == "__main__":
    main()
