"""
Script de configuración para Infología
"""
import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Verifica la versión de Python"""
    if sys.version_info < (3, 7):
        print("❌ Error: Se requiere Python 3.7 o superior")
        print(f"Versión actual: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def install_requirements():
    """Instala las dependencias"""
    print("📦 Instalando dependencias...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencias instaladas correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False


def create_directories():
    """Crea los directorios necesarios"""
    print("📁 Creando directorios...")
    directories = ["templates", "data", "output", "examples"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Directorio creado: {directory}")


def test_imports():
    """Prueba las importaciones principales"""
    print("🧪 Probando importaciones...")
    
    try:
        import tkinter
        print("✅ tkinter disponible")
    except ImportError:
        print("⚠️  tkinter no disponible - La interfaz gráfica no funcionará")
        print("   Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   CentOS/RHEL: sudo yum install tkinter")
    
    try:
        from PIL import Image
        print("✅ PIL/Pillow disponible")
    except ImportError:
        print("❌ PIL/Pillow no disponible")
        return False
    
    try:
        from docx import Document
        print("✅ python-docx disponible")
    except ImportError:
        print("❌ python-docx no disponible")
        return False
    
    try:
        from pptx import Presentation
        print("✅ python-pptx disponible")
    except ImportError:
        print("❌ python-pptx no disponible")
        return False
    
    try:
        import rembg
        print("✅ rembg disponible")
    except ImportError:
        print("❌ rembg no disponible")
        return False
    
    return True


def download_rembg_models():
    """Descarga los modelos de rembg"""
    print("🤖 Descargando modelos de IA para remoción de fondos...")
    print("   (Esto puede tomar varios minutos en la primera ejecución)")
    
    try:
        # Importar rembg para que descargue los modelos
        from rembg import remove
        from PIL import Image
        import io
        
        # Crear una imagen de prueba pequeña
        test_image = Image.new('RGB', (100, 100), color='white')
        img_byte_arr = io.BytesIO()
        test_image.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()
        
        # Esto forzará la descarga del modelo
        remove(img_byte_arr)
        print("✅ Modelos de IA descargados correctamente")
        return True
        
    except Exception as e:
        print(f"⚠️  Error descargando modelos: {e}")
        print("   Los modelos se descargarán automáticamente en el primer uso")
        return True  # No es crítico


def create_example_files():
    """Crea archivos de ejemplo si no existen"""
    print("📄 Verificando archivos de ejemplo...")
    
    example_file = Path("examples/caso_clinico_ejemplo.txt")
    if example_file.exists():
        print("✅ Archivo de ejemplo ya existe")
    else:
        print("ℹ️  Archivo de ejemplo creado en examples/")


def run_basic_test():
    """Ejecuta una prueba básica del sistema"""
    print("🧪 Ejecutando prueba básica...")
    
    try:
        # Importar módulos principales
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        from src.text_processing.clinical_text_processor import ClinicalTextProcessor
        from src.image_processing.image_processor import ClinicalImageProcessor
        from src.report_generation.report_generator import ClinicalReportGenerator
        
        # Crear instancias
        text_processor = ClinicalTextProcessor()
        image_processor = ClinicalImageProcessor()
        report_generator = ClinicalReportGenerator()
        
        print("✅ Todos los módulos se importaron correctamente")
        
        # Probar procesamiento de texto con el ejemplo
        example_file = Path("examples/caso_clinico_ejemplo.txt")
        if example_file.exists():
            clinical_data = text_processor.process_clinical_text(str(example_file))
            print(f"✅ Procesamiento de texto: {len(clinical_data['sections'])} secciones encontradas")
            
            if clinical_data['imc_data']:
                print(f"✅ Cálculo de IMC: {clinical_data['imc_data']['imc']}")
        
        print("✅ Prueba básica completada exitosamente")
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba básica: {e}")
        return False


def main():
    """Función principal de configuración"""
    print("🏥 Infología - Configuración del Sistema")
    print("=" * 50)
    
    # Verificar Python
    if not check_python_version():
        sys.exit(1)
    
    # Crear directorios
    create_directories()
    
    # Instalar dependencias
    if not install_requirements():
        print("❌ Error en la instalación. Verifique su conexión a internet.")
        sys.exit(1)
    
    # Probar importaciones
    if not test_imports():
        print("❌ Error en las importaciones. Verifique la instalación.")
        sys.exit(1)
    
    # Descargar modelos de IA
    download_rembg_models()
    
    # Crear archivos de ejemplo
    create_example_files()
    
    # Ejecutar prueba básica
    if not run_basic_test():
        print("⚠️  Advertencia: La prueba básica falló")
    
    print("\n" + "=" * 50)
    print("🎉 ¡Configuración completada!")
    print("\nPara ejecutar la aplicación:")
    print("  Modo gráfico:        python main.py")
    print("  Modo línea comandos: python main.py --cli --help")
    print("  Ejemplo:             python main.py --cli --text-file examples/caso_clinico_ejemplo.txt")
    print("\n📖 Consulte README.md para más información")


if __name__ == "__main__":
    main()
