"""
Creador de plantillas PowerPoint para informes odontológicos
"""
from pathlib import Path
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from ..config import POWERPOINT_CONFIG, TEMPLATES_DIR


class PowerPointTemplateCreator:
    """Creador de plantillas PowerPoint profesionales para informes odontológicos"""
    
    def __init__(self):
        self.template_name = POWERPOINT_CONFIG["template_name"]
        self.slide_width = Inches(POWERPOINT_CONFIG["slide_width"])
        self.slide_height = Inches(POWERPOINT_CONFIG["slide_height"])
        self.font_name = POWERPOINT_CONFIG["font_name"]
        self.title_font_size = Pt(POWERPOINT_CONFIG["title_font_size"])
        self.content_font_size = Pt(POWERPOINT_CONFIG["content_font_size"])
        
        # Colores corporativos (azul médico y gris)
        self.primary_color = RGBColor(41, 128, 185)  # Azul médico
        self.secondary_color = RGBColor(52, 73, 94)  # Gris oscuro
        self.accent_color = RGBColor(46, 204, 113)   # Verde médico
        self.light_gray = RGBColor(236, 240, 241)    # Gris claro
    
    def create_base_template(self) -> Presentation:
        """Crea la presentación base con configuración personalizada"""
        prs = Presentation()
        
        # Configurar tamaño de diapositiva
        prs.slide_width = self.slide_width
        prs.slide_height = self.slide_height
        
        return prs
    
    def add_title_slide(self, prs: Presentation) -> None:
        """Agrega diapositiva de título"""
        slide_layout = prs.slide_layouts[0]  # Layout de título
        slide = prs.slides.add_slide(slide_layout)
        
        # Título principal
        title = slide.shapes.title
        title.text = "INFORME CLÍNICO ODONTOLÓGICO"
        title_paragraph = title.text_frame.paragraphs[0]
        title_paragraph.font.name = self.font_name
        title_paragraph.font.size = Pt(32)
        title_paragraph.font.color.rgb = self.primary_color
        title_paragraph.font.bold = True
        title_paragraph.alignment = PP_ALIGN.CENTER
        
        # Subtítulo
        subtitle = slide.placeholders[1]
        subtitle.text = "Sistema Automatizado de Informes\nInfología v1.0"
        subtitle_paragraph = subtitle.text_frame.paragraphs[0]
        subtitle_paragraph.font.name = self.font_name
        subtitle_paragraph.font.size = Pt(18)
        subtitle_paragraph.font.color.rgb = self.secondary_color
        subtitle_paragraph.alignment = PP_ALIGN.CENTER
        
        # Agregar logo/forma decorativa
        self._add_decorative_shape(slide, Inches(1), Inches(6), Inches(8), Inches(0.5))
    
    def add_patient_info_slide(self, prs: Presentation) -> None:
        """Agrega diapositiva de información del paciente"""
        slide_layout = prs.slide_layouts[1]  # Layout de contenido
        slide = prs.slides.add_slide(slide_layout)
        
        # Título
        title = slide.shapes.title
        title.text = "INFORMACIÓN DEL PACIENTE"
        self._format_slide_title(title)
        
        # Contenido
        content = slide.placeholders[1]
        content.text = """Nombre: [NOMBRE_PACIENTE]
Edad: [EDAD_PACIENTE]
Fecha de nacimiento: [FECHA_NACIMIENTO]
Documento de identidad: [DOCUMENTO_ID]
Fecha de consulta: [FECHA_CONSULTA]
Médico tratante: [MEDICO_TRATANTE]

Datos antropométricos:
• Peso: [PESO] kg
• Talla: [TALLA] m
• IMC: [IMC] - [CLASIFICACION_IMC]"""
        
        self._format_content_text(content)
    
    def add_clinical_sections_slides(self, prs: Presentation) -> None:
        """Agrega diapositivas para las secciones clínicas principales"""
        
        sections = [
            ("MOTIVO DE CONSULTA", "[MOTIVO_CONSULTA]"),
            ("ENFERMEDAD ACTUAL", "[ENFERMEDAD_ACTUAL]"),
            ("ANTECEDENTES", """Antecedentes Familiares:
[ANTECEDENTES_FAMILIARES]

Antecedentes Personales:
[ANTECEDENTES_PERSONALES]

Antecedentes Médicos:
[ANTECEDENTES_MEDICOS]

Antecedentes Odontológicos:
[ANTECEDENTES_ODONTOLOGICOS]"""),
            ("EXAMEN FÍSICO", """Examen Extraoral:
[EXAMEN_EXTRAORAL]

Examen Intraoral:
[EXAMEN_INTRAORAL]"""),
            ("DIAGNÓSTICO Y PLAN", """Diagnóstico:
[DIAGNOSTICO]

Plan de Tratamiento:
[PLAN_TRATAMIENTO]

Pronóstico:
[PRONOSTICO]""")
        ]
        
        for section_title, section_content in sections:
            slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(slide_layout)
            
            # Título
            title = slide.shapes.title
            title.text = section_title
            self._format_slide_title(title)
            
            # Contenido
            content = slide.placeholders[1]
            content.text = section_content
            self._format_content_text(content)
    
    def add_images_slide(self, prs: Presentation) -> None:
        """Agrega diapositiva para evidencia fotográfica"""
        slide_layout = prs.slide_layouts[6]  # Layout en blanco
        slide = prs.slides.add_slide(slide_layout)
        
        # Título manual
        title_box = slide.shapes.add_textbox(Inches(1), Inches(0.5), Inches(8), Inches(1))
        title_frame = title_box.text_frame
        title_frame.text = "EVIDENCIA CLÍNICA FOTOGRÁFICA"
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.font.name = self.font_name
        title_paragraph.font.size = self.title_font_size
        title_paragraph.font.color.rgb = self.primary_color
        title_paragraph.font.bold = True
        title_paragraph.alignment = PP_ALIGN.CENTER
        
        # Placeholders para imágenes (2x2 grid)
        image_positions = [
            (Inches(0.5), Inches(2), Inches(4), Inches(2.5)),    # Top left
            (Inches(5), Inches(2), Inches(4), Inches(2.5)),      # Top right
            (Inches(0.5), Inches(4.8), Inches(4), Inches(2.5)),  # Bottom left
            (Inches(5), Inches(4.8), Inches(4), Inches(2.5))     # Bottom right
        ]
        
        for i, (left, top, width, height) in enumerate(image_positions):
            # Crear rectángulo placeholder
            placeholder = slide.shapes.add_shape(
                MSO_SHAPE.RECTANGLE, left, top, width, height
            )
            placeholder.fill.solid()
            placeholder.fill.fore_color.rgb = self.light_gray
            placeholder.line.color.rgb = self.primary_color
            placeholder.line.width = Pt(2)
            
            # Agregar texto placeholder
            text_frame = placeholder.text_frame
            text_frame.text = f"[IMAGEN_{i+1}]"
            text_paragraph = text_frame.paragraphs[0]
            text_paragraph.font.name = self.font_name
            text_paragraph.font.size = Pt(12)
            text_paragraph.font.color.rgb = self.secondary_color
            text_paragraph.alignment = PP_ALIGN.CENTER
            text_frame.vertical_anchor = 1  # Centro vertical
    
    def add_observations_slide(self, prs: Presentation) -> None:
        """Agrega diapositiva de observaciones finales"""
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)
        
        # Título
        title = slide.shapes.title
        title.text = "OBSERVACIONES Y RECOMENDACIONES"
        self._format_slide_title(title)
        
        # Contenido
        content = slide.placeholders[1]
        content.text = """Observaciones:
[OBSERVACIONES]

Recomendaciones:
• [RECOMENDACION_1]
• [RECOMENDACION_2]
• [RECOMENDACION_3]

Próxima cita: [PROXIMA_CITA]
Firma del profesional: [FIRMA_PROFESIONAL]"""
        
        self._format_content_text(content)
    
    def _format_slide_title(self, title_shape) -> None:
        """Formatea el título de una diapositiva"""
        title_paragraph = title_shape.text_frame.paragraphs[0]
        title_paragraph.font.name = self.font_name
        title_paragraph.font.size = self.title_font_size
        title_paragraph.font.color.rgb = self.primary_color
        title_paragraph.font.bold = True
        title_paragraph.alignment = PP_ALIGN.CENTER
    
    def _format_content_text(self, content_shape) -> None:
        """Formatea el texto de contenido"""
        content_frame = content_shape.text_frame
        for paragraph in content_frame.paragraphs:
            paragraph.font.name = self.font_name
            paragraph.font.size = self.content_font_size
            paragraph.font.color.rgb = self.secondary_color
            paragraph.space_after = Pt(6)
    
    def _add_decorative_shape(self, slide, left, top, width, height) -> None:
        """Agrega forma decorativa a la diapositiva"""
        shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, left, top, width, height
        )
        shape.fill.solid()
        shape.fill.fore_color.rgb = self.accent_color
        shape.line.fill.background()
    
    def create_complete_template(self, output_path: str = None) -> str:
        """Crea la plantilla completa y la guarda"""
        if not output_path:
            output_path = str(TEMPLATES_DIR / self.template_name)
        
        # Crear presentación base
        prs = self.create_base_template()
        
        # Agregar todas las diapositivas
        self.add_title_slide(prs)
        self.add_patient_info_slide(prs)
        self.add_clinical_sections_slides(prs)
        self.add_images_slide(prs)
        self.add_observations_slide(prs)
        
        # Guardar plantilla
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        prs.save(output_path)
        
        return output_path
