"""
Configuración global del sistema Infología
"""
import os
from pathlib import Path

# Rutas del proyecto
PROJECT_ROOT = Path(__file__).parent.parent
TEMPLATES_DIR = PROJECT_ROOT / "templates"
DATA_DIR = PROJECT_ROOT / "data"
OUTPUT_DIR = PROJECT_ROOT / "output"
EXAMPLES_DIR = PROJECT_ROOT / "examples"

# Configuración de procesamiento de imágenes
IMAGE_CONFIG = {
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"],
    "output_format": "PNG",
    "max_width": 800,
    "max_height": 600,
    "quality": 95
}

# Configuración de texto clínico
TEXT_CONFIG = {
    "supported_formats": [".txt", ".docx"],
    "sections": [
        "motivo de consulta",
        "enfermedad actual", 
        "antecedentes familiares",
        "antecedentes personales",
        "antecedentes médicos",
        "antecedentes odontológicos",
        "examen físico",
        "examen intraoral",
        "examen extraoral",
        "diagnóstico",
        "plan de tratamiento",
        "pronóstico",
        "observaciones"
    ]
}

# Configuración de IMC según OMS
IMC_CLASSIFICATION = {
    "bajo_peso": (0, 18.5),
    "normal": (18.5, 25.0),
    "sobrepeso": (25.0, 30.0),
    "obesidad_grado_i": (30.0, 35.0),
    "obesidad_grado_ii": (35.0, 40.0),
    "obesidad_grado_iii": (40.0, float('inf'))
}

# Configuración de PowerPoint
POWERPOINT_CONFIG = {
    "template_name": "informe_odontologico_template.pptx",
    "slide_width": 10,  # pulgadas
    "slide_height": 7.5,  # pulgadas
    "font_name": "Calibri",
    "title_font_size": 24,
    "content_font_size": 14
}

# Crear directorios si no existen
for directory in [TEMPLATES_DIR, DATA_DIR, OUTPUT_DIR, EXAMPLES_DIR]:
    directory.mkdir(exist_ok=True)
