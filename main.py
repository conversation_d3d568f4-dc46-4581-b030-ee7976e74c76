#!/usr/bin/env python3
"""
Infología - Sistema de Automatización de Informes Clínicos Odontológicos

Este script principal permite ejecutar la aplicación tanto en modo gráfico
como en modo línea de comandos.

Uso:
    python main.py                    # Modo gráfico (GUI)
    python main.py --cli              # Modo línea de comandos
    python main.py --help             # Mostrar ayuda
"""

import sys
import argparse
from pathlib import Path

# Agregar el directorio src al path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ui.main_window import InfologiaMainWindow
from src.text_processing.clinical_text_processor import ClinicalTextProcessor
from src.image_processing.image_processor import ClinicalImageProcessor
from src.report_generation.report_generator import ClinicalReportGenerator


def run_gui():
    """Ejecuta la aplicación en modo gráfico"""
    try:
        app = InfologiaMainWindow()
        app.run()
    except ImportError as e:
        print(f"Error: No se pudo cargar la interfaz gráfica: {e}")
        print("Asegúrese de tener tkinter instalado.")
        print("En Ubuntu/Debian: sudo apt-get install python3-tk")
        sys.exit(1)
    except Exception as e:
        print(f"Error ejecutando la aplicación: {e}")
        sys.exit(1)


def run_visual():
    """Ejecuta la nueva interfaz visual con plantilla en pantalla"""
    try:
        from src.ui.visual_interface import VisualInterface
        app = VisualInterface()
        app.run()
    except ImportError as e:
        print(f"Error: No se pudo cargar la interfaz visual: {e}")
        print("Asegúrese de tener tkinter y PIL instalados")
        sys.exit(1)
    except Exception as e:
        print(f"Error ejecutando la interfaz visual: {e}")
        sys.exit(1)


def run_cli(args):
    """Ejecuta la aplicación en modo línea de comandos"""
    try:
        # Validar argumentos
        if not args.text_file:
            print("Error: Debe especificar un archivo de texto con --text-file")
            sys.exit(1)
        
        if not Path(args.text_file).exists():
            print(f"Error: El archivo {args.text_file} no existe")
            sys.exit(1)
        
        print("=== Infología - Generador de Informes Clínicos ===")
        print(f"Procesando archivo: {args.text_file}")
        
        # Inicializar procesadores
        text_processor = ClinicalTextProcessor()
        image_processor = ClinicalImageProcessor()
        report_generator = ClinicalReportGenerator()
        
        # Procesar texto clínico
        print("Procesando texto clínico...")
        clinical_data = text_processor.process_clinical_text(args.text_file)
        
        print(f"Secciones encontradas: {len(clinical_data['sections'])}")
        for section in clinical_data['sections'].keys():
            print(f"  - {section.title()}")
        
        if clinical_data['imc_data']:
            imc_data = clinical_data['imc_data']
            print(f"IMC calculado: {imc_data['imc']} ({imc_data['clasificacion']})")
        
        # Procesar imágenes si se especificaron
        processed_images = []
        if args.images:
            print(f"Procesando {len(args.images)} imágenes...")
            processed_images = image_processor.process_multiple_images(
                args.images,
                remove_bg=not args.no_remove_bg,
                crop=True,
                resize=True,
                enhance=True
            )
            print(f"Imágenes procesadas: {len(processed_images)}")
        
        # Preparar información del paciente
        patient_info = {}
        if args.patient_name:
            patient_info['nombre'] = args.patient_name
        if args.patient_age:
            patient_info['edad'] = args.patient_age
        if args.doctor_name:
            patient_info['medico'] = args.doctor_name
        
        # Generar informe
        print("Generando informe PowerPoint...")
        output_file = report_generator.generate_report(
            clinical_data=clinical_data,
            image_paths=processed_images,
            patient_info=patient_info if patient_info else None,
            output_filename=args.output
        )
        
        print(f"✓ Informe generado exitosamente: {output_file}")
        
        # Generar PDF si se solicita
        if args.pdf:
            print("Generando archivo PDF...")
            pdf_file = report_generator.convert_to_pdf(output_file)
            if pdf_file:
                print(f"✓ PDF generado: {pdf_file}")
            else:
                print("⚠ No se pudo generar el PDF")
        
        print("¡Proceso completado exitosamente!")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


def main():
    """Función principal"""
    parser = argparse.ArgumentParser(
        description="Infología - Sistema de Automatización de Informes Clínicos Odontológicos",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:

  Modo gráfico (por defecto):
    python main.py

  Modo línea de comandos básico:
    python main.py --cli --text-file "data/caso_clinico.txt"

  Con imágenes:
    python main.py --cli --text-file "data/caso_clinico.txt" --images "img1.jpg" "img2.jpg"

  Con información del paciente:
    python main.py --cli --text-file "data/caso_clinico.txt" --patient-name "Juan Pérez" --patient-age "35"

  Generar también PDF:
    python main.py --cli --text-file "data/caso_clinico.txt" --pdf
        """
    )
    
    parser.add_argument('--cli', action='store_true',
                       help='Ejecutar en modo línea de comandos')

    parser.add_argument('--classic', action='store_true',
                       help='Usar interfaz clásica (pestañas)')
    
    parser.add_argument('--text-file', type=str,
                       help='Archivo de texto clínico (.txt o .docx)')
    
    parser.add_argument('--images', nargs='*', type=str,
                       help='Lista de archivos de imagen')
    
    parser.add_argument('--output', type=str,
                       help='Nombre del archivo de salida (opcional)')
    
    parser.add_argument('--patient-name', type=str,
                       help='Nombre del paciente')
    
    parser.add_argument('--patient-age', type=str,
                       help='Edad del paciente')
    
    parser.add_argument('--doctor-name', type=str,
                       help='Nombre del médico tratante')
    
    parser.add_argument('--no-remove-bg', action='store_true',
                       help='No remover fondo de las imágenes')
    
    parser.add_argument('--pdf', action='store_true',
                       help='Generar también archivo PDF')
    
    parser.add_argument('--version', action='version', version='Infología 1.0.0')
    
    args = parser.parse_args()
    
    # Determinar qué interfaz ejecutar
    if args.cli:
        run_cli(args)
    elif args.classic:
        run_gui()
    else:
        # Por defecto usar la nueva interfaz visual
        run_visual()


if __name__ == "__main__":
    main()
