"""
Procesador de imágenes clínicas para informes odontológicos
"""
import os
import cv2
import numpy as np
from pathlib import Path
from typing import List, Tuple, Optional
from PIL import Image, ImageEnhance
from rembg import remove
from ..config import IMAGE_CONFIG


class ClinicalImageProcessor:
    """Procesador de imágenes clínicas que remueve fondos y estandariza"""
    
    def __init__(self):
        self.supported_formats = IMAGE_CONFIG["supported_formats"]
        self.output_format = IMAGE_CONFIG["output_format"]
        self.max_width = IMAGE_CONFIG["max_width"]
        self.max_height = IMAGE_CONFIG["max_height"]
        self.quality = IMAGE_CONFIG["quality"]
    
    def is_supported_format(self, file_path: str) -> bool:
        """Verifica si el formato de archivo es soportado"""
        return Path(file_path).suffix.lower() in self.supported_formats
    
    def load_image(self, file_path: str) -> Image.Image:
        """Carga una imagen desde archivo"""
        if not Path(file_path).exists():
            raise FileNotFoundError(f"La imagen {file_path} no existe")
        
        if not self.is_supported_format(file_path):
            raise ValueError(f"Formato no soportado: {Path(file_path).suffix}")
        
        return Image.open(file_path).convert("RGB")
    
    def remove_background(self, image: Image.Image) -> Image.Image:
        """Remueve el fondo de la imagen usando rembg"""
        try:
            # Convertir PIL a bytes
            import io
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()
            
            # Remover fondo
            output = remove(img_byte_arr)
            
            # Convertir de vuelta a PIL
            return Image.open(io.BytesIO(output)).convert("RGBA")
        except Exception as e:
            print(f"Error removiendo fondo: {e}")
            # Si falla, retornar imagen original
            return image.convert("RGBA")
    
    def crop_to_content(self, image: Image.Image) -> Image.Image:
        """Recorta la imagen al área con contenido (elimina espacios vacíos)"""
        if image.mode != "RGBA":
            image = image.convert("RGBA")
        
        # Obtener el bounding box del contenido no transparente
        bbox = image.getbbox()
        
        if bbox:
            return image.crop(bbox)
        else:
            return image
    
    def resize_image(self, image: Image.Image, max_width: int = None, max_height: int = None) -> Image.Image:
        """Redimensiona la imagen manteniendo la proporción"""
        max_w = max_width or self.max_width
        max_h = max_height or self.max_height
        
        # Calcular nuevo tamaño manteniendo proporción
        width, height = image.size
        ratio = min(max_w / width, max_h / height)
        
        if ratio < 1:  # Solo redimensionar si es necesario
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            return image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return image
    
    def enhance_image(self, image: Image.Image) -> Image.Image:
        """Mejora la calidad de la imagen (contraste, nitidez)"""
        if image.mode == "RGBA":
            # Trabajar solo con RGB para el enhancement
            rgb_image = Image.new("RGB", image.size, (255, 255, 255))
            rgb_image.paste(image, mask=image.split()[-1])  # Usar canal alpha como máscara
            
            # Mejorar contraste
            enhancer = ImageEnhance.Contrast(rgb_image)
            rgb_image = enhancer.enhance(1.1)
            
            # Mejorar nitidez
            enhancer = ImageEnhance.Sharpness(rgb_image)
            rgb_image = enhancer.enhance(1.1)
            
            # Convertir de vuelta a RGBA
            result = rgb_image.convert("RGBA")
            # Aplicar la máscara alpha original
            result.putalpha(image.split()[-1])
            return result
        else:
            # Para imágenes RGB normales
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            return image
    
    def process_single_image(self, input_path: str, output_path: str = None, 
                           remove_bg: bool = True, crop: bool = True, 
                           resize: bool = True, enhance: bool = True) -> str:
        """Procesa una sola imagen con todas las transformaciones"""
        # Cargar imagen
        image = self.load_image(input_path)
        
        # Remover fondo si se solicita
        if remove_bg:
            image = self.remove_background(image)
        
        # Recortar al contenido
        if crop:
            image = self.crop_to_content(image)
        
        # Redimensionar
        if resize:
            image = self.resize_image(image)
        
        # Mejorar calidad
        if enhance:
            image = self.enhance_image(image)
        
        # Determinar ruta de salida
        if not output_path:
            input_path_obj = Path(input_path)
            output_path = str(input_path_obj.parent / f"{input_path_obj.stem}_processed.png")
        
        # Guardar imagen procesada
        if image.mode == "RGBA":
            image.save(output_path, "PNG", quality=self.quality)
        else:
            image.save(output_path, self.output_format, quality=self.quality)
        
        return output_path
    
    def process_multiple_images(self, input_paths: List[str], output_dir: str = None,
                              remove_bg: bool = True, crop: bool = True,
                              resize: bool = True, enhance: bool = True) -> List[str]:
        """Procesa múltiples imágenes"""
        if output_dir:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        processed_paths = []
        
        for i, input_path in enumerate(input_paths):
            try:
                if output_dir:
                    input_name = Path(input_path).stem
                    output_path = str(Path(output_dir) / f"{input_name}_processed.png")
                else:
                    output_path = None
                
                processed_path = self.process_single_image(
                    input_path, output_path, remove_bg, crop, resize, enhance
                )
                processed_paths.append(processed_path)
                print(f"Procesada imagen {i+1}/{len(input_paths)}: {Path(input_path).name}")
                
            except Exception as e:
                print(f"Error procesando {input_path}: {e}")
                continue
        
        return processed_paths
    
    def create_image_grid(self, image_paths: List[str], output_path: str, 
                         cols: int = 2, spacing: int = 10) -> str:
        """Crea una grilla con múltiples imágenes"""
        if not image_paths:
            raise ValueError("No se proporcionaron imágenes")
        
        # Cargar todas las imágenes
        images = []
        for path in image_paths:
            try:
                img = Image.open(path).convert("RGBA")
                images.append(img)
            except Exception as e:
                print(f"Error cargando {path}: {e}")
                continue
        
        if not images:
            raise ValueError("No se pudieron cargar las imágenes")
        
        # Calcular dimensiones de la grilla
        rows = (len(images) + cols - 1) // cols
        
        # Encontrar el tamaño máximo para estandarizar
        max_width = max(img.width for img in images)
        max_height = max(img.height for img in images)
        
        # Redimensionar todas las imágenes al mismo tamaño
        standardized_images = []
        for img in images:
            # Redimensionar manteniendo proporción
            ratio = min(max_width / img.width, max_height / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            
            resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Crear imagen con fondo blanco del tamaño estándar
            standard_img = Image.new("RGBA", (max_width, max_height), (255, 255, 255, 255))
            
            # Centrar la imagen redimensionada
            x_offset = (max_width - new_width) // 2
            y_offset = (max_height - new_height) // 2
            standard_img.paste(resized, (x_offset, y_offset), resized)
            
            standardized_images.append(standard_img)
        
        # Crear la imagen de la grilla
        grid_width = cols * max_width + (cols - 1) * spacing
        grid_height = rows * max_height + (rows - 1) * spacing
        
        grid_image = Image.new("RGBA", (grid_width, grid_height), (255, 255, 255, 255))
        
        # Colocar las imágenes en la grilla
        for i, img in enumerate(standardized_images):
            row = i // cols
            col = i % cols
            
            x = col * (max_width + spacing)
            y = row * (max_height + spacing)
            
            grid_image.paste(img, (x, y), img)
        
        # Guardar la grilla
        grid_image.save(output_path, "PNG", quality=self.quality)
        return output_path
