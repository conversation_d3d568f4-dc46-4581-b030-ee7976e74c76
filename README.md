# Infología - Sistema de Automatización de Informes Clínicos Odontológicos

Infología es un sistema automatizado que permite generar informes clínicos odontológicos profesionales a partir de texto e imágenes, utilizando **tu plantilla personalizada** `plantillaejemplo.pptx`.

## 🎨 Plantilla Personalizada

El sistema está configurado para usar tu plantilla específica con:
- **42 diapositivas** profesionales
- **Diseño médico** especializado en odontología
- **Secciones predefinidas** para casos clínicos
- **Espacios para evidencia fotográfica**
- **Formato académico** completo

## 🚀 Características Principales

- **Procesamiento de texto inteligente**: Identifica automáticamente secciones clínicas
- **Cálculo automático de IMC**: Calcula y clasifica el IMC según estándares de la OMS
- **Procesamiento de imágenes**: Remueve fondos automáticamente y estandariza imágenes
- **Generación de informes**: Crea presentaciones PowerPoint profesionales
- **Interfaz gráfica amigable**: Fácil de usar para profesionales de la salud
- **Modo línea de comandos**: Para automatización y procesamiento por lotes

## 📋 Requisitos del Sistema

- Python 3.7 o superior
- Windows, macOS o Linux
- Conexión a internet (para la primera instalación de modelos de IA)

## 🛠️ Instalación Paso a Paso

### Paso 1: Preparar el entorno
```bash
# Asegúrese de tener Python 3.7+ instalado
python --version

# Navegue al directorio del proyecto
cd infologia
```

### Paso 2: Configuración automática
```bash
# Ejecute el script de configuración (RECOMENDADO)
python setup.py
```

**O instalación manual:**
```bash
# Instalar dependencias manualmente
pip install -r requirements.txt
```

### Paso 3: Verificar plantilla
- Asegúrese de que `plantillaejemplo.pptx` esté en el directorio del proyecto
- El script de configuración la copiará automáticamente a `templates/`

### Paso 4: Ejecutar la aplicación
```bash
# Nueva interfaz visual con plantilla en pantalla (RECOMENDADO)
python main.py

# Interfaz clásica con pestañas
python main.py --classic

# Línea de comandos
python main.py --cli --text-file "examples/caso_edna_patricia.txt"

# Demostración completa
python demo.py
```

## ✨ **NUEVA INTERFAZ VISUAL**

🎨 **Plantilla en pantalla** - Ve tu informe mientras lo creas
🖼️ **Arrastrar y soltar** - Agrega imágenes directamente en la plantilla
🎯 **Ajuste automático** - Las imágenes se adaptan perfectamente
💝 **Dedicatoria incluida** - "Para: Mi amor" automáticamente
🧹 **Interfaz simplificada** - Solo lo esencial, sin complicaciones

## 📖 Uso

### ✨ Interfaz Visual (NUEVA - Recomendada)

1. Ejecutar `python main.py`
2. **Panel izquierdo**:
   - Completar datos del paciente
   - Escribir contenido clínico directamente
   - Configurar opciones (remover fondo, etc.)
3. **Panel derecho**:
   - Ver plantilla en tiempo real
   - Navegar entre diapositivas (◀ ▶)
   - Clic derecho → "Agregar Imagen"
   - Arrastrar imágenes para ajustar posición
4. Hacer clic en "🚀 GENERAR INFORME"

### 📁 Interfaz Clásica (Pestañas)

1. Ejecutar `python main.py --classic`
2. En la pestaña "Archivos de Entrada":
   - Seleccionar archivo de texto clínico (.txt o .docx)
   - Agregar imágenes clínicas (opcional)
3. En la pestaña "Información del Paciente":
   - Completar datos del paciente
4. En la pestaña "Configuración":
   - Ajustar opciones de procesamiento
5. Hacer clic en "Generar Informe"

### Modo Línea de Comandos

```bash
# Ejemplo básico
python main.py --cli --text-file "examples/caso_clinico_ejemplo.txt"

# Con imágenes
python main.py --cli --text-file "data/caso.txt" --images "img1.jpg" "img2.jpg"

# Con información del paciente
python main.py --cli --text-file "data/caso.txt" --patient-name "Juan Pérez" --patient-age "35"

# Generar también PDF
python main.py --cli --text-file "data/caso.txt" --pdf
```

## 📁 Estructura del Proyecto

```
infologia/
├── src/
│   ├── text_processing/          # Procesamiento de texto clínico
│   ├── image_processing/         # Procesamiento de imágenes
│   ├── report_generation/        # Generación de informes PowerPoint
│   ├── ui/                       # Interfaz gráfica
│   └── config.py                 # Configuración global
├── templates/                    # Plantillas PowerPoint
├── data/                         # Datos de entrada
├── output/                       # Informes generados
├── examples/                     # Ejemplos de uso
├── main.py                       # Script principal
├── requirements.txt              # Dependencias
└── README.md                     # Este archivo
```

## 📝 Formato del Archivo de Texto

El archivo de texto debe contener las secciones clínicas con sus respectivos encabezados:

```
Motivo de consulta:
[Descripción del motivo de consulta]

Enfermedad actual:
[Descripción de la enfermedad actual]

Antecedentes familiares:
[Antecedentes familiares relevantes]

Examen físico:
Peso: 75 kg
Talla: 1.75 m
[Otros hallazgos del examen físico]

Diagnóstico:
[Diagnóstico principal y secundarios]

Plan de tratamiento:
[Plan terapéutico propuesto]
```

## 🖼️ Procesamiento de Imágenes

El sistema procesa automáticamente las imágenes:

- **Remoción de fondo**: Utiliza IA para remover fondos automáticamente
- **Recorte inteligente**: Elimina espacios vacíos
- **Estandarización**: Redimensiona y mejora la calidad
- **Formatos soportados**: JPG, PNG, BMP, TIFF

## 📊 Cálculo de IMC

Si el texto contiene peso y talla, el sistema:

- Calcula automáticamente el IMC
- Clasifica según estándares de la OMS:
  - Bajo peso: < 18.5
  - Normal: 18.5 - 24.9
  - Sobrepeso: 25.0 - 29.9
  - Obesidad Grado I: 30.0 - 34.9
  - Obesidad Grado II: 35.0 - 39.9
  - Obesidad Grado III: ≥ 40.0

## 🎨 Personalización

### Modificar la Plantilla PowerPoint

1. Editar `src/report_generation/template_creator.py`
2. Personalizar colores, fuentes y diseño
3. Regenerar la plantilla ejecutando el sistema

### Agregar Nuevas Secciones Clínicas

1. Modificar `src/config.py` en la sección `TEXT_CONFIG["sections"]`
2. Actualizar la plantilla PowerPoint correspondiente

## 🔧 Solución de Problemas

### Error: "No se pudo cargar la interfaz gráfica"
- **Ubuntu/Debian**: `sudo apt-get install python3-tk`
- **CentOS/RHEL**: `sudo yum install tkinter`

### Error: "No se pudo remover el fondo"
- Verificar conexión a internet (primera ejecución)
- Reinstalar rembg: `pip uninstall rembg && pip install rembg`

### Error: "No se pudo generar PDF"
- **Windows**: Instalar `pip install comtypes`
- **macOS/Linux**: Funcionalidad limitada, usar conversores externos

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:

1. Fork el proyecto
2. Crear una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 👥 Autores

- **Equipo Infología** - Desarrollo inicial

## 🙏 Agradecimientos

- Comunidad de Python por las excelentes librerías
- Desarrolladores de rembg por la tecnología de remoción de fondos
- Profesionales de la salud que proporcionaron feedback

---

**Infología v1.0.0** - Sistema de Automatización de Informes Clínicos Odontológicos
