#!/usr/bin/env python3
"""
Analizador de plantilla PowerPoint existente
"""
import sys
from pathlib import Path
from pptx import Presentation

def analyze_template(template_path: str):
    """Analiza la estructura de la plantilla PowerPoint"""
    print(f"🔍 ANALIZANDO PLANTILLA: {template_path}")
    print("=" * 60)
    
    try:
        prs = Presentation(template_path)
        
        print(f"📊 Información general:")
        print(f"  • Número de diapositivas: {len(prs.slides)}")
        print(f"  • Tamaño: {prs.slide_width.inches:.1f}\" x {prs.slide_height.inches:.1f}\"")
        print(f"  • Layouts disponibles: {len(prs.slide_layouts)}")
        
        print(f"\n📋 ANÁLISIS DE DIAPOSITIVAS:")
        print("-" * 40)
        
        for i, slide in enumerate(prs.slides):
            print(f"\n🔸 DIAPOSITIVA {i+1}:")
            
            # Analizar formas en la diapositiva
            shapes_info = []
            text_content = []
            
            for j, shape in enumerate(slide.shapes):
                shape_info = f"  Shape {j+1}: {shape.shape_type}"
                
                # Si tiene texto
                if hasattr(shape, 'text') and shape.text.strip():
                    shape_info += f" - Texto: '{shape.text[:50]}...'"
                    text_content.append(shape.text)
                
                # Si es un text frame
                elif hasattr(shape, 'text_frame'):
                    full_text = ""
                    for paragraph in shape.text_frame.paragraphs:
                        for run in paragraph.runs:
                            full_text += run.text
                    if full_text.strip():
                        shape_info += f" - TextFrame: '{full_text[:50]}...'"
                        text_content.append(full_text)
                
                # Si es una imagen
                if shape.shape_type == 13:  # MSO_SHAPE_TYPE.PICTURE
                    shape_info += " [IMAGEN]"
                
                # Si es una tabla
                elif shape.shape_type == 19:  # MSO_SHAPE_TYPE.TABLE
                    shape_info += " [TABLA]"
                
                shapes_info.append(shape_info)
            
            # Mostrar información de formas
            print(f"  📝 Formas encontradas: {len(slide.shapes)}")
            for shape_info in shapes_info:
                print(shape_info)
            
            # Mostrar todo el texto de la diapositiva
            if text_content:
                print(f"  📄 Contenido de texto completo:")
                for text in text_content:
                    if text.strip():
                        print(f"    '{text.strip()}'")
        
        print(f"\n🎨 LAYOUTS DISPONIBLES:")
        print("-" * 30)
        for i, layout in enumerate(prs.slide_layouts):
            print(f"  Layout {i}: {layout.name}")
            print(f"    Placeholders: {len(layout.placeholders)}")
            for j, placeholder in enumerate(layout.placeholders):
                print(f"      {j}: {placeholder.name} (tipo: {placeholder.placeholder_format.type})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analizando plantilla: {e}")
        return False

def extract_placeholders(template_path: str):
    """Extrae todos los placeholders de texto de la plantilla"""
    print(f"\n🔍 EXTRAYENDO PLACEHOLDERS...")
    print("-" * 40)
    
    try:
        prs = Presentation(template_path)
        placeholders_found = set()
        
        for i, slide in enumerate(prs.slides):
            for shape in slide.shapes:
                # Buscar en texto directo
                if hasattr(shape, 'text') and shape.text:
                    text = shape.text
                    # Buscar patrones como [PLACEHOLDER] o {PLACEHOLDER}
                    import re
                    patterns = re.findall(r'\[([^\]]+)\]|\{([^}]+)\}', text)
                    for pattern in patterns:
                        placeholder = pattern[0] or pattern[1]
                        if placeholder:
                            placeholders_found.add(f"[{placeholder}]")
                
                # Buscar en text_frame
                elif hasattr(shape, 'text_frame'):
                    for paragraph in shape.text_frame.paragraphs:
                        for run in paragraph.runs:
                            text = run.text
                            import re
                            patterns = re.findall(r'\[([^\]]+)\]|\{([^}]+)\}', text)
                            for pattern in patterns:
                                placeholder = pattern[0] or pattern[1]
                                if placeholder:
                                    placeholders_found.add(f"[{placeholder}]")
        
        print(f"📋 Placeholders encontrados ({len(placeholders_found)}):")
        for placeholder in sorted(placeholders_found):
            print(f"  • {placeholder}")
        
        return sorted(placeholders_found)
        
    except Exception as e:
        print(f"❌ Error extrayendo placeholders: {e}")
        return []

def main():
    """Función principal"""
    template_path = "plantillaejemplo.pptx"
    
    if not Path(template_path).exists():
        print(f"❌ No se encontró la plantilla: {template_path}")
        return
    
    # Analizar estructura
    if analyze_template(template_path):
        # Extraer placeholders
        placeholders = extract_placeholders(template_path)
        
        print(f"\n💡 RECOMENDACIONES:")
        print("-" * 30)
        print("1. La plantilla será copiada a templates/plantillaejemplo.pptx")
        print("2. El sistema buscará y reemplazará los placeholders encontrados")
        print("3. Se adaptará el código para usar esta plantilla como base")
        
        if placeholders:
            print(f"\n📝 Placeholders que el sistema puede reemplazar:")
            for placeholder in placeholders:
                print(f"  ✅ {placeholder}")
        else:
            print(f"\n⚠️  No se encontraron placeholders estándar [TEXTO]")
            print("   El sistema agregará placeholders automáticamente")

if __name__ == "__main__":
    main()
