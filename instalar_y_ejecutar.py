#!/usr/bin/env python3
"""
Script de instalación y ejecución paso a paso para Infología
"""
import os
import sys
import subprocess
from pathlib import Path


def print_header():
    """Imprime el encabezado del programa"""
    print("🏥" + "=" * 68 + "🏥")
    print("🏥  INFOLOGÍA - SISTEMA DE INFORMES CLÍNICOS ODONTOLÓGICOS  🏥")
    print("🏥" + "=" * 68 + "🏥")
    print("🎨 Usando tu plantilla personalizada: plantillaejemplo.pptx")
    print("=" * 70)


def check_python():
    """Verifica la versión de Python"""
    print("🐍 Verificando Python...")
    if sys.version_info < (3, 7):
        print("❌ Error: Se requiere Python 3.7 o superior")
        print(f"   Versión actual: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detectado")
    return True


def check_template():
    """Verifica que la plantilla esté disponible"""
    print("\n🎨 Verificando plantilla...")
    
    template_in_templates = Path("templates/plantillaejemplo.pptx")
    template_in_root = Path("plantillaejemplo.pptx")
    
    if template_in_templates.exists():
        print("✅ Plantilla encontrada en templates/")
        return True
    elif template_in_root.exists():
        print("📁 Plantilla encontrada en directorio raíz")
        print("   Copiando a templates/...")
        Path("templates").mkdir(exist_ok=True)
        import shutil
        shutil.copy2(template_in_root, template_in_templates)
        print("✅ Plantilla copiada exitosamente")
        return True
    else:
        print("❌ No se encontró plantillaejemplo.pptx")
        print("   Por favor, asegúrese de tener el archivo en el directorio del proyecto")
        return False


def install_dependencies():
    """Instala las dependencias"""
    print("\n📦 Instalando dependencias...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "python-pptx", "python-docx", "Pillow", "rembg", 
            "opencv-python", "reportlab", "--quiet"
        ])
        print("✅ Dependencias instaladas correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False


def test_imports():
    """Prueba las importaciones principales"""
    print("\n🧪 Probando importaciones...")
    
    modules = [
        ("pptx", "python-pptx"),
        ("docx", "python-docx"), 
        ("PIL", "Pillow"),
        ("rembg", "rembg"),
        ("cv2", "opencv-python")
    ]
    
    for module, package in modules:
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} no disponible")
            return False
    
    return True


def create_directories():
    """Crea directorios necesarios"""
    print("\n📁 Creando directorios...")
    directories = ["templates", "data", "output", "examples"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}/")


def show_menu():
    """Muestra el menú de opciones"""
    print("\n" + "=" * 70)
    print("🚀 ¡INSTALACIÓN COMPLETADA! ¿Qué desea hacer?")
    print("=" * 70)
    print("1. 🖥️  Abrir interfaz gráfica (RECOMENDADO)")
    print("2. 🎬 Ver demostración completa")
    print("3. ⚡ Ejemplo rápido con línea de comandos")
    print("4. 📖 Ver ayuda detallada")
    print("5. 🚪 Salir")
    print("=" * 70)


def run_gui():
    """Ejecuta la interfaz gráfica"""
    print("\n🖥️  Iniciando interfaz gráfica...")
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n⏹️  Aplicación cerrada por el usuario")
    except Exception as e:
        print(f"❌ Error ejecutando interfaz gráfica: {e}")


def run_demo():
    """Ejecuta la demostración"""
    print("\n🎬 Iniciando demostración completa...")
    try:
        subprocess.run([sys.executable, "demo.py"])
    except KeyboardInterrupt:
        print("\n⏹️  Demostración interrumpida")
    except Exception as e:
        print(f"❌ Error ejecutando demostración: {e}")


def run_cli_example():
    """Ejecuta ejemplo de línea de comandos"""
    print("\n⚡ Ejecutando ejemplo rápido...")
    try:
        subprocess.run([
            sys.executable, "main.py", "--cli", 
            "--text-file", "examples/caso_clinico_ejemplo.txt"
        ])
    except Exception as e:
        print(f"❌ Error ejecutando ejemplo: {e}")


def show_help():
    """Muestra ayuda detallada"""
    print("\n📖 AYUDA DETALLADA")
    print("=" * 50)
    print("🖥️  Interfaz gráfica:")
    print("   python main.py")
    print("\n⚡ Línea de comandos:")
    print("   python main.py --cli --text-file archivo.txt")
    print("   python main.py --cli --text-file archivo.txt --images img1.jpg img2.jpg")
    print("\n🎬 Demostración:")
    print("   python demo.py")
    print("\n🧪 Pruebas:")
    print("   python test_infologia.py")
    print("\n📁 Estructura de archivos:")
    print("   - Coloque archivos de texto en: data/")
    print("   - Coloque imágenes en: data/")
    print("   - Los informes se generan en: output/")
    print("   - Ejemplos disponibles en: examples/")


def main():
    """Función principal"""
    print_header()
    
    # Verificaciones iniciales
    if not check_python():
        input("\nPresione Enter para salir...")
        sys.exit(1)
    
    if not check_template():
        print("\n⚠️  Continuando sin plantilla personalizada...")
    
    # Instalación
    create_directories()
    
    if not install_dependencies():
        print("\n❌ Error en la instalación")
        input("Presione Enter para salir...")
        sys.exit(1)
    
    if not test_imports():
        print("\n❌ Error en las importaciones")
        input("Presione Enter para salir...")
        sys.exit(1)
    
    # Menú principal
    while True:
        show_menu()
        
        try:
            choice = input("\n👉 Seleccione una opción (1-5): ").strip()
            
            if choice == "1":
                run_gui()
            elif choice == "2":
                run_demo()
            elif choice == "3":
                run_cli_example()
            elif choice == "4":
                show_help()
            elif choice == "5":
                print("\n👋 ¡Gracias por usar Infología!")
                break
            else:
                print("❌ Opción inválida. Por favor seleccione 1-5.")
                
        except KeyboardInterrupt:
            print("\n\n👋 ¡Hasta luego!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
        
        input("\nPresione Enter para continuar...")


if __name__ == "__main__":
    main()
